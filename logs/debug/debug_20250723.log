2025-07-23 10:16:59 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-23 10:17:00 | DEBUG | testcases.test_ella.base_ella_test:_detect_command_type:176 | 检测到命令类型: flashlight (flashlight状态)
2025-07-23 10:17:01 | DEBUG | testcases.test_ella.base_ella_test:_get_status_by_type:214 | 获取flashlight状态(初始): False
2025-07-23 10:17:01 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-23 10:17:01 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-23 10:17:01 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-23 10:17:01 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-23 10:17:02 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-23 10:17:02 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-23 10:17:02 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [发送按钮]: True
2025-07-23 10:17:03 | DEBUG | pages.apps.ella.ella_response_handler:_extract_text_attributes_from_xml:424 | 正则提取到 14 个文本: ['10:17', 'Dialogue', 'Explore', '10:16', "Hi, I'm Ella", 'I can answer your questions, summarize content, and provide creative inspiration.', 'Refresh', 'Federal Grants to Nonprofits Slashed', 'How to use Ask About Screen', 'Grizzlies Sign Ty Jerome']...
2025-07-23 10:17:03 | DEBUG | testcases.test_ella.base_ella_test:_detect_command_type:176 | 检测到命令类型: flashlight (flashlight状态)
2025-07-23 10:17:03 | DEBUG | testcases.test_ella.base_ella_test:_get_status_by_type:203 | 等待状态变化: 3.0秒
2025-07-23 10:17:07 | DEBUG | testcases.test_ella.base_ella_test:_get_status_by_type:214 | 获取flashlight状态(最终): False
2025-07-23 10:17:07 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-23 10:17:07 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-23 10:17:08 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从asr_txt节点获取响应 (第1次)
2025-07-23 10:17:08 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:256 | 从asr_txt节点获取到原始文本: "open flashlight"
2025-07-23 10:17:08 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:285 | asr_txt文本不符合AI响应格式: open flashlight (第1次尝试)
2025-07-23 10:17:08 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从asr_txt节点获取响应 (第2次)
2025-07-23 10:17:08 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:256 | 从asr_txt节点获取到原始文本: "open flashlight"
2025-07-23 10:17:08 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:285 | asr_txt文本不符合AI响应格式: open flashlight (第2次尝试)
2025-07-23 10:17:09 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从asr_txt节点获取响应 (第3次)
2025-07-23 10:17:09 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:256 | 从asr_txt节点获取到原始文本: "open flashlight"
2025-07-23 10:17:09 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:285 | asr_txt文本不符合AI响应格式: open flashlight (第3次尝试)
2025-07-23 10:17:09 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从robot_text节点获取响应 (第1次)
2025-07-23 10:17:09 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:256 | 从robot_text节点获取到原始文本: "Flashlight is turned on now."
2025-07-23 10:17:09 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:285 | robot_text文本不符合AI响应格式: Flashlight is turned on now. (第1次尝试)
2025-07-23 10:17:10 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从robot_text节点获取响应 (第2次)
2025-07-23 10:17:10 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:256 | 从robot_text节点获取到原始文本: "Flashlight is turned on now."
2025-07-23 10:17:10 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:285 | robot_text文本不符合AI响应格式: Flashlight is turned on now. (第2次尝试)
2025-07-23 10:17:10 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从robot_text节点获取响应 (第3次)
2025-07-23 10:17:11 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:256 | 从robot_text节点获取到原始文本: "Flashlight is turned on now."
2025-07-23 10:17:11 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:285 | robot_text文本不符合AI响应格式: Flashlight is turned on now. (第3次尝试)
2025-07-23 10:17:11 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从function_name节点获取响应 (第1次)
2025-07-23 10:17:11 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:256 | 从function_name节点获取到原始文本: "Flashlight"
2025-07-23 10:17:11 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:285 | function_name文本不符合AI响应格式: Flashlight (第1次尝试)
2025-07-23 10:17:11 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从function_name节点获取响应 (第2次)
2025-07-23 10:17:12 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:256 | 从function_name节点获取到原始文本: "Flashlight"
2025-07-23 10:17:12 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:285 | function_name文本不符合AI响应格式: Flashlight (第2次尝试)
2025-07-23 10:17:12 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从function_name节点获取响应 (第3次)
2025-07-23 10:17:12 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:256 | 从function_name节点获取到原始文本: "Flashlight"
2025-07-23 10:17:12 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:285 | function_name文本不符合AI响应格式: Flashlight (第3次尝试)
2025-07-23 10:17:12 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从function_control节点获取响应 (第1次)
2025-07-23 10:17:13 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:256 | 从function_control节点获取到原始文本: "None"
2025-07-23 10:17:13 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:260 | function_control节点文本为空 (第1次尝试)
2025-07-23 10:17:13 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从function_control节点获取响应 (第2次)
2025-07-23 10:17:13 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:256 | 从function_control节点获取到原始文本: "None"
2025-07-23 10:17:13 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:260 | function_control节点文本为空 (第2次尝试)
2025-07-23 10:17:14 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从function_control节点获取响应 (第3次)
2025-07-23 10:17:14 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:256 | 从function_control节点获取到原始文本: "None"
2025-07-23 10:17:14 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:260 | function_control节点文本为空 (第3次尝试)
2025-07-23 10:17:14 | DEBUG | testcases.test_ella.base_ella_test:verify_expected_in_response:510 | 响应文本(列表转换): 原始列表=['open flashlight', 'Flashlight is turned on now.', 'Flashlight', ''], 过滤后=['open flashlight', 'Flashlight is turned on now.', 'Flashlight'], 合并后=open flashlight Flashlight is turned on now. Flashlight
2025-07-23 10:25:44 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-23 10:25:45 | DEBUG | testcases.test_ella.base_ella_test:_detect_command_type:176 | 检测到命令类型: flashlight (flashlight状态)
2025-07-23 10:25:45 | DEBUG | testcases.test_ella.base_ella_test:_get_status_by_type:214 | 获取flashlight状态(初始): True
2025-07-23 10:25:46 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-23 10:25:46 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-23 10:25:46 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-23 10:25:46 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-23 10:25:46 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-23 10:25:47 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-23 10:25:47 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [发送按钮]: True
2025-07-23 10:25:49 | DEBUG | pages.apps.ella.ella_response_handler:_extract_text_attributes_from_xml:424 | 正则提取到 14 个文本: ['10:25', 'Dialogue', 'Explore', 'What is Ask About Screen?', 'GOP Split on Epstein Probe', 'Haliburton Reacts to Turner Trade', 'open flashlight', 'Flashlight is turned on now.', 'Flashlight', 'Set Up']...
2025-07-23 10:25:50 | DEBUG | pages.apps.ella.ella_response_handler:_extract_text_attributes_from_xml:424 | 正则提取到 14 个文本: ['10:25', 'Dialogue', 'Explore', 'What is Ask About Screen?', 'GOP Split on Epstein Probe', 'Haliburton Reacts to Turner Trade', 'open flashlight', 'Flashlight is turned on now.', 'Flashlight', 'Set Up']...
2025-07-23 10:25:50 | DEBUG | testcases.test_ella.base_ella_test:_detect_command_type:176 | 检测到命令类型: flashlight (flashlight状态)
2025-07-23 10:25:50 | DEBUG | testcases.test_ella.base_ella_test:_get_status_by_type:203 | 等待状态变化: 3.0秒
2025-07-23 10:25:53 | DEBUG | testcases.test_ella.base_ella_test:_get_status_by_type:214 | 获取flashlight状态(最终): True
2025-07-23 10:25:53 | DEBUG | core.base_element:is_exists:114 | 元素存在性检查 [输入框]: True
2025-07-23 10:25:53 | DEBUG | pages.apps.ella.main_page_refactored:_check_chat_page_indicators:289 | 检测到输入框，可能在对话页面
2025-07-23 10:25:54 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从asr_txt节点获取响应 (第1次)
2025-07-23 10:25:54 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:256 | 从asr_txt节点获取到原始文本: "open flashlight"
2025-07-23 10:25:54 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:285 | asr_txt文本不符合AI响应格式: open flashlight (第1次尝试)
2025-07-23 10:25:54 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从asr_txt节点获取响应 (第2次)
2025-07-23 10:25:55 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:256 | 从asr_txt节点获取到原始文本: "open flashlight"
2025-07-23 10:25:55 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:285 | asr_txt文本不符合AI响应格式: open flashlight (第2次尝试)
2025-07-23 10:25:55 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从asr_txt节点获取响应 (第3次)
2025-07-23 10:25:56 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:256 | 从asr_txt节点获取到原始文本: "open flashlight"
2025-07-23 10:25:56 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:285 | asr_txt文本不符合AI响应格式: open flashlight (第3次尝试)
2025-07-23 10:25:56 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从robot_text节点获取响应 (第1次)
2025-07-23 10:25:56 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:256 | 从robot_text节点获取到原始文本: "Flashlight is turned on now."
2025-07-23 10:25:56 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:285 | robot_text文本不符合AI响应格式: Flashlight is turned on now. (第1次尝试)
2025-07-23 10:25:56 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从robot_text节点获取响应 (第2次)
2025-07-23 10:25:57 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:256 | 从robot_text节点获取到原始文本: "Flashlight is turned on now."
2025-07-23 10:25:57 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:285 | robot_text文本不符合AI响应格式: Flashlight is turned on now. (第2次尝试)
2025-07-23 10:25:57 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从robot_text节点获取响应 (第3次)
2025-07-23 10:25:57 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:256 | 从robot_text节点获取到原始文本: "Flashlight is turned on now."
2025-07-23 10:25:57 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:285 | robot_text文本不符合AI响应格式: Flashlight is turned on now. (第3次尝试)
2025-07-23 10:25:57 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从function_name节点获取响应 (第1次)
2025-07-23 10:25:58 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:256 | 从function_name节点获取到原始文本: "Flashlight"
2025-07-23 10:25:58 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:285 | function_name文本不符合AI响应格式: Flashlight (第1次尝试)
2025-07-23 10:25:58 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从function_name节点获取响应 (第2次)
2025-07-23 10:25:58 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:256 | 从function_name节点获取到原始文本: "Flashlight"
2025-07-23 10:25:58 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:285 | function_name文本不符合AI响应格式: Flashlight (第2次尝试)
2025-07-23 10:25:59 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从function_name节点获取响应 (第3次)
2025-07-23 10:25:59 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:256 | 从function_name节点获取到原始文本: "Flashlight"
2025-07-23 10:25:59 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:285 | function_name文本不符合AI响应格式: Flashlight (第3次尝试)
2025-07-23 10:25:59 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从function_control节点获取响应 (第1次)
2025-07-23 10:25:59 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:256 | 从function_control节点获取到原始文本: "None"
2025-07-23 10:25:59 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:260 | function_control节点文本为空 (第1次尝试)
2025-07-23 10:26:00 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从function_control节点获取响应 (第2次)
2025-07-23 10:26:00 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:256 | 从function_control节点获取到原始文本: "None"
2025-07-23 10:26:00 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:260 | function_control节点文本为空 (第2次尝试)
2025-07-23 10:26:01 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:239 | 尝试从function_control节点获取响应 (第3次)
2025-07-23 10:26:01 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:256 | 从function_control节点获取到原始文本: "None"
2025-07-23 10:26:01 | DEBUG | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:260 | function_control节点文本为空 (第3次尝试)
2025-07-23 10:26:01 | DEBUG | testcases.test_ella.base_ella_test:verify_expected_in_response:510 | 响应文本(列表转换): 原始列表=['open flashlight', 'Flashlight is turned on now.', 'Flashlight', ''], 过滤后=['open flashlight', 'Flashlight is turned on now.', 'Flashlight'], 合并后=open flashlight Flashlight is turned on now. Flashlight
