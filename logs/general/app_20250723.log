2025-07-23 10:16:50 | INFO | core.base_driver:_discover_and_update_current_device:319 | 🔍 主动识别当前连接的设备...
2025-07-23 10:16:50 | INFO | utils.device_discovery:discover_all_devices:288 | 开始发现所有连接的设备...
2025-07-23 10:16:51 | INFO | utils.device_discovery:get_connected_devices:49 | 发现 1 个连接的设备: ['13764254B4001229']
2025-07-23 10:16:51 | INFO | utils.device_discovery:discover_device:243 | 正在发现设备: 13764254B4001229
2025-07-23 10:16:54 | INFO | utils.device_discovery:extract_hios_version:187 | 提取到HiOS版本: 15.1.2 (来源: CM8-15.1.2.017(OP001PF001AZ)_SU)
2025-07-23 10:16:54 | INFO | utils.device_discovery:discover_device:274 | 设备发现完成: TECNO CM8 (HiOS: 15.1.2)
2025-07-23 10:16:54 | INFO | utils.device_discovery:discover_all_devices:305 | 设备发现完成，共发现 1 个设备
2025-07-23 10:16:54 | INFO | core.base_driver:_discover_and_update_current_device:333 | 发现设备: TECNO CM8 (13764254B4001229)
2025-07-23 10:16:54 | INFO | core.base_driver:_discover_and_update_current_device:344 | 设备已存在于配置中: current_device
2025-07-23 10:16:54 | INFO | core.base_driver:_discover_and_update_current_device:350 | ✅ 设备 'current_device' 已是当前设备
2025-07-23 10:16:54 | INFO | core.base_driver:_load_device_config:98 | 加载设备配置: current_device
2025-07-23 10:16:54 | INFO | core.base_driver:_load_device_config:99 |   设备名称: TECNO CM8
2025-07-23 10:16:54 | INFO | core.base_driver:_load_device_config:100 |   设备ID: 13764254B4001229
2025-07-23 10:16:54 | INFO | core.base_driver:_load_device_config:101 |   HiOS版本: 15.0.3
2025-07-23 10:16:54 | INFO | core.base_driver:_connect_device:43 | 正在连接指定设备: TECNO CM8 (13764254B4001229)
2025-07-23 10:16:54 | INFO | core.base_driver:_ensure_uiautomator2_service:147 | 检查UIAutomator2服务状态...
2025-07-23 10:16:54 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 13764254B4001229)
2025-07-23 10:16:54 | INFO | utils.uiautomator2_manager:check_service_health:53 | ✅ UIAutomator2服务健康状态良好
2025-07-23 10:16:54 | INFO | core.base_driver:_ensure_uiautomator2_service:154 | ✅ UIAutomator2服务状态正常
2025-07-23 10:16:55 | INFO | core.base_driver:_connect_device:53 | 设备连接成功: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-23 10:16:55 | INFO | core.base_driver:_verify_device_info:134 | ✅ 设备验证通过
2025-07-23 10:16:55 | INFO | core.base_driver:_setup_implicit_wait:189 | 设置隐式等待时间: 10秒
2025-07-23 10:16:55 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-07-23 10:16:55 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-07-23 10:16:55 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-07-23 10:16:55 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-23 10:16:55 | INFO | core.base_page:__init__:38 | 初始化页面: ella - main_page
2025-07-23 10:16:55 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:121 | 启动Ella应用（指定Activity）
2025-07-23 10:16:55 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:129 | 尝试启动Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-23 10:16:59 | INFO | pages.apps.ella.ella_status_checker:check_app_started:524 | ✅ 应用已在前台: com.transsion.aivoiceassistant
2025-07-23 10:16:59 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:134 | ✅ Ella应用启动成功（指定Activity）
2025-07-23 10:16:59 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:169 | 等待Ella页面加载完成，超时时间: 15秒
2025-07-23 10:16:59 | INFO | pages.apps.ella.ella_status_checker:check_service_health:481 | 检查UIAutomator2服务健康状态
2025-07-23 10:16:59 | INFO | pages.apps.ella.ella_status_checker:check_service_health:497 | ✅ UIAutomator2服务健康状态良好
2025-07-23 10:16:59 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:178 | 当前应用包名: com.transsion.aivoiceassistant
2025-07-23 10:16:59 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:181 | ✅ Ella应用包已确认
2025-07-23 10:16:59 | INFO | pages.apps.ella.main_page_refactored:_wait_for_ui_elements:203 | 等待Ella页面UI元素加载...
2025-07-23 10:16:59 | INFO | pages.apps.ella.main_page_refactored:_wait_for_ui_elements:224 | ✅ 检测到页面元素: 输入框
2025-07-23 10:17:00 | INFO | testcases.test_ella.base_ella_test:ella_app:26 | ✅ Ella应用启动成功
2025-07-23 10:17:00 | INFO | pages.apps.ella.ella_status_checker:check_flashlight_status:127 | 检查手电筒状态
2025-07-23 10:17:01 | INFO | pages.apps.ella.ella_status_checker:check_flashlight_status:214 | 手电筒状态: 关闭 (所有检测方法均未发现开启状态)
2025-07-23 10:17:01 | INFO | testcases.test_ella.base_ella_test:execute_command_and_verify:53 | 初始状态 - 使用命令open flashlight，状态: False
2025-07-23 10:17:01 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:253 | 确保在对话页面...
2025-07-23 10:17:01 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:442 | 检查当前进程是否是Ella...
2025-07-23 10:17:01 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:449 | 当前应用: com.transsion.aivoiceassistant
2025-07-23 10:17:01 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:450 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-23 10:17:01 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:459 | ✅ 当前在Ella应用进程
2025-07-23 10:17:01 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:264 | ✅ 已在对话页面
2025-07-23 10:17:01 | INFO | pages.apps.ella.ella_command_executor:_ensure_input_box_ready:130 | 确保输入框就绪...
2025-07-23 10:17:01 | INFO | pages.apps.ella.ella_command_executor:_check_known_input_elements:160 | ✅ 找到主输入框
2025-07-23 10:17:01 | INFO | pages.apps.ella.ella_command_executor:execute_text_command:34 | 执行文本命令: open flashlight
2025-07-23 10:17:01 | INFO | pages.apps.ella.ella_command_executor:_ensure_input_box_ready:130 | 确保输入框就绪...
2025-07-23 10:17:02 | INFO | pages.apps.ella.ella_command_executor:_check_known_input_elements:160 | ✅ 找到主输入框
2025-07-23 10:17:02 | INFO | pages.apps.ella.ella_command_executor:_input_text_command:230 | 输入文本命令: open flashlight
2025-07-23 10:17:02 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-23 10:17:02 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-23 10:17:02 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-23 10:17:02 | INFO | core.base_element:clear_text:325 | 清空文本成功 [输入框]
2025-07-23 10:17:02 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-23 10:17:02 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-23 10:17:02 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-23 10:17:02 | INFO | core.base_element:send_keys:302 | 输入文本成功 [输入框]: open flashlight
2025-07-23 10:17:02 | INFO | pages.apps.ella.ella_command_executor:_input_text_command:247 | ✅ 文本输入成功
2025-07-23 10:17:02 | INFO | pages.apps.ella.ella_command_executor:_send_command:321 | 发送命令
2025-07-23 10:17:03 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-07-23 10:17:03 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-23 10:17:03 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-07-23 10:17:03 | INFO | core.base_element:click:231 | 点击元素成功 [发送按钮]
2025-07-23 10:17:03 | INFO | pages.apps.ella.ella_command_executor:_send_command:327 | ✅ 点击发送按钮成功
2025-07-23 10:17:03 | INFO | pages.apps.ella.ella_command_executor:execute_text_command:53 | ✅ 文本命令执行完成
2025-07-23 10:17:03 | INFO | testcases.test_ella.base_ella_test:_execute_command:353 | ✅ 成功执行命令: open flashlight
2025-07-23 10:17:03 | INFO | pages.apps.ella.ella_response_handler:wait_for_response:33 | 等待AI响应，超时时间: 8秒
2025-07-23 10:17:03 | INFO | pages.apps.ella.ella_response_handler:wait_for_response:48 | ✅ 通过元素数量变化检测到响应
2025-07-23 10:17:06 | INFO | pages.apps.ella.ella_status_checker:check_flashlight_status:127 | 检查手电筒状态
2025-07-23 10:17:07 | INFO | pages.apps.ella.ella_status_checker:check_flashlight_status:214 | 手电筒状态: 关闭 (所有检测方法均未发现开启状态)
2025-07-23 10:17:07 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:90 | 状态检查完成，现在获取响应文本
2025-07-23 10:17:07 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:94 | 第1次尝试确保在Ella页面以获取响应
2025-07-23 10:17:07 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:253 | 确保在对话页面...
2025-07-23 10:17:07 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:442 | 检查当前进程是否是Ella...
2025-07-23 10:17:07 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:449 | 当前应用: com.transsion.aivoiceassistant
2025-07-23 10:17:07 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:450 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-23 10:17:07 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:459 | ✅ 当前在Ella应用进程
2025-07-23 10:17:07 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:264 | ✅ 已在对话页面
2025-07-23 10:17:07 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:98 | ✅ 已确认在Ella对话页面，可以获取响应
2025-07-23 10:17:07 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:442 | 检查当前进程是否是Ella...
2025-07-23 10:17:08 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:449 | 当前应用: com.transsion.aivoiceassistant
2025-07-23 10:17:08 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:450 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-23 10:17:08 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:459 | ✅ 当前在Ella应用进程
2025-07-23 10:17:08 | INFO | pages.apps.ella.ella_response_handler:get_response_all_text:129 | 获取AI响应文本
2025-07-23 10:17:09 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:290 | asr_txt文本不符合AI响应格式: open flashlight，已达到最大重试次数
2025-07-23 10:17:11 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:290 | robot_text文本不符合AI响应格式: Flashlight is turned on now.，已达到最大重试次数
2025-07-23 10:17:12 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:290 | function_name文本不符合AI响应格式: Flashlight，已达到最大重试次数
2025-07-23 10:17:14 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:265 | function_control节点文本为空，已达到最大重试次数
2025-07-23 10:17:14 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:119 | 最终获取的AI响应: '['open flashlight', 'Flashlight is turned on now.', 'Flashlight', '']'
2025-07-23 10:17:14 | INFO | testcases.test_ella.base_ella_test:_verify_status_change:453 | ✅ 状态验证通过: False -> False
2025-07-23 10:17:14 | INFO | testcases.test_ella.base_ella_test:simple_command_test:678 | 🎉 open flashlight 测试完成
2025-07-23 10:17:14 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:490 | verify_expected_in_response 响应类型: <class 'list'>, 内容: ['open flashlight', 'Flashlight is turned on now.', 'Flashlight', '']
2025-07-23 10:17:14 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:534 | ✅ 响应包含期望内容: 'flashlight'
2025-07-23 10:17:14 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:542 | 🎉 所有期望内容都已找到 (1/1)
2025-07-23 10:17:14 | INFO | core.base_page:stop_app:91 | 停止应用: ella (com.transsion.aivoiceassistant)
2025-07-23 10:17:14 | INFO | core.base_driver:stop_app:247 | 停止应用: com.transsion.aivoiceassistant
2025-07-23 10:17:15 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-07-23 10:17:15 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-07-23 10:17:15 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
2025-07-23 10:18:41 | INFO | core.base_driver:_discover_and_update_current_device:319 | 🔍 主动识别当前连接的设备...
2025-07-23 10:18:41 | INFO | utils.device_discovery:discover_all_devices:288 | 开始发现所有连接的设备...
2025-07-23 10:18:41 | INFO | utils.device_discovery:get_connected_devices:49 | 发现 1 个连接的设备: ['13764254B4001229']
2025-07-23 10:18:41 | INFO | utils.device_discovery:discover_device:243 | 正在发现设备: 13764254B4001229
2025-07-23 10:18:44 | INFO | utils.device_discovery:extract_hios_version:187 | 提取到HiOS版本: 15.1.2 (来源: CM8-15.1.2.017(OP001PF001AZ)_SU)
2025-07-23 10:18:44 | INFO | utils.device_discovery:discover_device:274 | 设备发现完成: TECNO CM8 (HiOS: 15.1.2)
2025-07-23 10:18:44 | INFO | utils.device_discovery:discover_all_devices:305 | 设备发现完成，共发现 1 个设备
2025-07-23 10:18:44 | INFO | core.base_driver:_discover_and_update_current_device:333 | 发现设备: TECNO CM8 (13764254B4001229)
2025-07-23 10:18:44 | INFO | core.base_driver:_discover_and_update_current_device:344 | 设备已存在于配置中: current_device
2025-07-23 10:18:44 | INFO | core.base_driver:_discover_and_update_current_device:350 | ✅ 设备 'current_device' 已是当前设备
2025-07-23 10:18:44 | INFO | core.base_driver:_load_device_config:98 | 加载设备配置: current_device
2025-07-23 10:18:44 | INFO | core.base_driver:_load_device_config:99 |   设备名称: TECNO CM8
2025-07-23 10:18:44 | INFO | core.base_driver:_load_device_config:100 |   设备ID: 13764254B4001229
2025-07-23 10:18:44 | INFO | core.base_driver:_load_device_config:101 |   HiOS版本: 15.0.3
2025-07-23 10:18:44 | INFO | core.base_driver:_connect_device:43 | 正在连接指定设备: TECNO CM8 (13764254B4001229)
2025-07-23 10:18:44 | INFO | core.base_driver:_ensure_uiautomator2_service:147 | 检查UIAutomator2服务状态...
2025-07-23 10:18:44 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 13764254B4001229)
2025-07-23 10:18:45 | INFO | utils.uiautomator2_manager:check_service_health:53 | ✅ UIAutomator2服务健康状态良好
2025-07-23 10:18:45 | INFO | core.base_driver:_ensure_uiautomator2_service:154 | ✅ UIAutomator2服务状态正常
2025-07-23 10:18:45 | INFO | core.base_driver:_connect_device:53 | 设备连接成功: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-23 10:18:45 | INFO | core.base_driver:_verify_device_info:134 | ✅ 设备验证通过
2025-07-23 10:18:45 | INFO | core.base_driver:_setup_implicit_wait:189 | 设置隐式等待时间: 10秒
2025-07-23 10:18:45 | INFO | __main__:check_flashlight_status:127 | 检查手电筒状态
2025-07-23 10:18:45 | INFO | __main__:check_flashlight_status:214 | 手电筒状态: 关闭 (所有检测方法均未发现开启状态)
2025-07-23 10:18:45 | INFO | __main__:get_flashlight_detailed_status:229 | 获取手电筒详细状态
2025-07-23 10:18:45 | INFO | __main__:check_flashlight_status:127 | 检查手电筒状态
2025-07-23 10:18:46 | INFO | __main__:check_flashlight_status:214 | 手电筒状态: 关闭 (所有检测方法均未发现开启状态)
2025-07-23 10:19:40 | INFO | core.base_driver:_discover_and_update_current_device:319 | 🔍 主动识别当前连接的设备...
2025-07-23 10:19:40 | INFO | utils.device_discovery:discover_all_devices:288 | 开始发现所有连接的设备...
2025-07-23 10:19:40 | INFO | utils.device_discovery:get_connected_devices:49 | 发现 1 个连接的设备: ['13764254B4001229']
2025-07-23 10:19:40 | INFO | utils.device_discovery:discover_device:243 | 正在发现设备: 13764254B4001229
2025-07-23 10:19:43 | INFO | utils.device_discovery:extract_hios_version:187 | 提取到HiOS版本: 15.1.2 (来源: CM8-15.1.2.017(OP001PF001AZ)_SU)
2025-07-23 10:19:43 | INFO | utils.device_discovery:discover_device:274 | 设备发现完成: TECNO CM8 (HiOS: 15.1.2)
2025-07-23 10:19:43 | INFO | utils.device_discovery:discover_all_devices:305 | 设备发现完成，共发现 1 个设备
2025-07-23 10:19:43 | INFO | core.base_driver:_discover_and_update_current_device:333 | 发现设备: TECNO CM8 (13764254B4001229)
2025-07-23 10:19:43 | INFO | core.base_driver:_discover_and_update_current_device:344 | 设备已存在于配置中: current_device
2025-07-23 10:19:43 | INFO | core.base_driver:_discover_and_update_current_device:350 | ✅ 设备 'current_device' 已是当前设备
2025-07-23 10:19:43 | INFO | core.base_driver:_load_device_config:98 | 加载设备配置: current_device
2025-07-23 10:19:43 | INFO | core.base_driver:_load_device_config:99 |   设备名称: TECNO CM8
2025-07-23 10:19:43 | INFO | core.base_driver:_load_device_config:100 |   设备ID: 13764254B4001229
2025-07-23 10:19:43 | INFO | core.base_driver:_load_device_config:101 |   HiOS版本: 15.0.3
2025-07-23 10:19:43 | INFO | core.base_driver:_connect_device:43 | 正在连接指定设备: TECNO CM8 (13764254B4001229)
2025-07-23 10:19:43 | INFO | core.base_driver:_ensure_uiautomator2_service:147 | 检查UIAutomator2服务状态...
2025-07-23 10:19:43 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 13764254B4001229)
2025-07-23 10:19:44 | INFO | utils.uiautomator2_manager:check_service_health:53 | ✅ UIAutomator2服务健康状态良好
2025-07-23 10:19:44 | INFO | core.base_driver:_ensure_uiautomator2_service:154 | ✅ UIAutomator2服务状态正常
2025-07-23 10:19:44 | INFO | core.base_driver:_connect_device:53 | 设备连接成功: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-23 10:19:44 | INFO | core.base_driver:_verify_device_info:134 | ✅ 设备验证通过
2025-07-23 10:19:44 | INFO | core.base_driver:_setup_implicit_wait:189 | 设置隐式等待时间: 10秒
2025-07-23 10:19:44 | INFO | __main__:check_flashlight_status:127 | 检查手电筒状态
2025-07-23 10:19:45 | INFO | __main__:check_flashlight_status:214 | 手电筒状态: 关闭 (所有检测方法均未发现开启状态)
2025-07-23 10:21:12 | INFO | core.base_driver:_discover_and_update_current_device:319 | 🔍 主动识别当前连接的设备...
2025-07-23 10:21:12 | INFO | utils.device_discovery:discover_all_devices:288 | 开始发现所有连接的设备...
2025-07-23 10:21:13 | INFO | utils.device_discovery:get_connected_devices:49 | 发现 1 个连接的设备: ['13764254B4001229']
2025-07-23 10:21:13 | INFO | utils.device_discovery:discover_device:243 | 正在发现设备: 13764254B4001229
2025-07-23 10:21:15 | INFO | utils.device_discovery:extract_hios_version:187 | 提取到HiOS版本: 15.1.2 (来源: CM8-15.1.2.017(OP001PF001AZ)_SU)
2025-07-23 10:21:15 | INFO | utils.device_discovery:discover_device:274 | 设备发现完成: TECNO CM8 (HiOS: 15.1.2)
2025-07-23 10:21:15 | INFO | utils.device_discovery:discover_all_devices:305 | 设备发现完成，共发现 1 个设备
2025-07-23 10:21:15 | INFO | core.base_driver:_discover_and_update_current_device:333 | 发现设备: TECNO CM8 (13764254B4001229)
2025-07-23 10:21:15 | INFO | core.base_driver:_discover_and_update_current_device:344 | 设备已存在于配置中: current_device
2025-07-23 10:21:15 | INFO | core.base_driver:_discover_and_update_current_device:350 | ✅ 设备 'current_device' 已是当前设备
2025-07-23 10:21:15 | INFO | core.base_driver:_load_device_config:98 | 加载设备配置: current_device
2025-07-23 10:21:15 | INFO | core.base_driver:_load_device_config:99 |   设备名称: TECNO CM8
2025-07-23 10:21:15 | INFO | core.base_driver:_load_device_config:100 |   设备ID: 13764254B4001229
2025-07-23 10:21:15 | INFO | core.base_driver:_load_device_config:101 |   HiOS版本: 15.0.3
2025-07-23 10:21:15 | INFO | core.base_driver:_connect_device:43 | 正在连接指定设备: TECNO CM8 (13764254B4001229)
2025-07-23 10:21:15 | INFO | core.base_driver:_ensure_uiautomator2_service:147 | 检查UIAutomator2服务状态...
2025-07-23 10:21:15 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 13764254B4001229)
2025-07-23 10:21:16 | INFO | utils.uiautomator2_manager:check_service_health:53 | ✅ UIAutomator2服务健康状态良好
2025-07-23 10:21:16 | INFO | core.base_driver:_ensure_uiautomator2_service:154 | ✅ UIAutomator2服务状态正常
2025-07-23 10:21:17 | INFO | core.base_driver:_connect_device:53 | 设备连接成功: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-23 10:21:17 | INFO | core.base_driver:_verify_device_info:134 | ✅ 设备验证通过
2025-07-23 10:21:17 | INFO | core.base_driver:_setup_implicit_wait:189 | 设置隐式等待时间: 10秒
2025-07-23 10:21:17 | INFO | pages.apps.ella.ella_status_checker:check_flashlight_status:127 | 检查手电筒状态
2025-07-23 10:21:17 | INFO | pages.apps.ella.ella_status_checker:check_flashlight_status:214 | 手电筒状态: 关闭 (所有检测方法均未发现开启状态)
2025-07-23 10:21:17 | INFO | pages.apps.ella.ella_status_checker:get_flashlight_detailed_status:229 | 获取手电筒详细状态
2025-07-23 10:21:17 | INFO | pages.apps.ella.ella_status_checker:check_flashlight_status:127 | 检查手电筒状态
2025-07-23 10:21:18 | INFO | pages.apps.ella.ella_status_checker:check_flashlight_status:214 | 手电筒状态: 关闭 (所有检测方法均未发现开启状态)
2025-07-23 10:23:51 | INFO | core.base_driver:_discover_and_update_current_device:319 | 🔍 主动识别当前连接的设备...
2025-07-23 10:23:51 | INFO | utils.device_discovery:discover_all_devices:288 | 开始发现所有连接的设备...
2025-07-23 10:23:51 | INFO | utils.device_discovery:get_connected_devices:49 | 发现 1 个连接的设备: ['13764254B4001229']
2025-07-23 10:23:51 | INFO | utils.device_discovery:discover_device:243 | 正在发现设备: 13764254B4001229
2025-07-23 10:23:53 | INFO | utils.device_discovery:extract_hios_version:187 | 提取到HiOS版本: 15.1.2 (来源: CM8-15.1.2.017(OP001PF001AZ)_SU)
2025-07-23 10:23:53 | INFO | utils.device_discovery:discover_device:274 | 设备发现完成: TECNO CM8 (HiOS: 15.1.2)
2025-07-23 10:23:53 | INFO | utils.device_discovery:discover_all_devices:305 | 设备发现完成，共发现 1 个设备
2025-07-23 10:23:53 | INFO | core.base_driver:_discover_and_update_current_device:333 | 发现设备: TECNO CM8 (13764254B4001229)
2025-07-23 10:23:53 | INFO | core.base_driver:_discover_and_update_current_device:344 | 设备已存在于配置中: current_device
2025-07-23 10:23:53 | INFO | core.base_driver:_discover_and_update_current_device:350 | ✅ 设备 'current_device' 已是当前设备
2025-07-23 10:23:53 | INFO | core.base_driver:_load_device_config:98 | 加载设备配置: current_device
2025-07-23 10:23:53 | INFO | core.base_driver:_load_device_config:99 |   设备名称: TECNO CM8
2025-07-23 10:23:53 | INFO | core.base_driver:_load_device_config:100 |   设备ID: 13764254B4001229
2025-07-23 10:23:53 | INFO | core.base_driver:_load_device_config:101 |   HiOS版本: 15.0.3
2025-07-23 10:23:53 | INFO | core.base_driver:_connect_device:43 | 正在连接指定设备: TECNO CM8 (13764254B4001229)
2025-07-23 10:23:53 | INFO | core.base_driver:_ensure_uiautomator2_service:147 | 检查UIAutomator2服务状态...
2025-07-23 10:23:53 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 13764254B4001229)
2025-07-23 10:23:54 | INFO | utils.uiautomator2_manager:check_service_health:53 | ✅ UIAutomator2服务健康状态良好
2025-07-23 10:23:54 | INFO | core.base_driver:_ensure_uiautomator2_service:154 | ✅ UIAutomator2服务状态正常
2025-07-23 10:23:55 | INFO | core.base_driver:_connect_device:53 | 设备连接成功: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-23 10:23:55 | INFO | core.base_driver:_verify_device_info:134 | ✅ 设备验证通过
2025-07-23 10:23:55 | INFO | core.base_driver:_setup_implicit_wait:189 | 设置隐式等待时间: 10秒
2025-07-23 10:23:55 | INFO | pages.apps.ella.ella_status_checker:check_flashlight_status:127 | 检查手电筒状态
2025-07-23 10:23:55 | INFO | pages.apps.ella.ella_status_checker:check_flashlight_status:159 | 手电筒状态: 开启 (检测到torch开启记录: 5条)
2025-07-23 10:23:55 | INFO | pages.apps.ella.ella_status_checker:get_flashlight_detailed_status:274 | 获取手电筒详细状态
2025-07-23 10:23:55 | INFO | pages.apps.ella.ella_status_checker:check_flashlight_status:127 | 检查手电筒状态
2025-07-23 10:23:55 | INFO | pages.apps.ella.ella_status_checker:check_flashlight_status:159 | 手电筒状态: 开启 (检测到torch开启记录: 5条)
2025-07-23 10:25:15 | INFO | core.base_driver:_discover_and_update_current_device:319 | 🔍 主动识别当前连接的设备...
2025-07-23 10:25:15 | INFO | utils.device_discovery:discover_all_devices:288 | 开始发现所有连接的设备...
2025-07-23 10:25:15 | INFO | utils.device_discovery:get_connected_devices:49 | 发现 1 个连接的设备: ['13764254B4001229']
2025-07-23 10:25:15 | INFO | utils.device_discovery:discover_device:243 | 正在发现设备: 13764254B4001229
2025-07-23 10:25:17 | INFO | utils.device_discovery:extract_hios_version:187 | 提取到HiOS版本: 15.1.2 (来源: CM8-15.1.2.017(OP001PF001AZ)_SU)
2025-07-23 10:25:17 | INFO | utils.device_discovery:discover_device:274 | 设备发现完成: TECNO CM8 (HiOS: 15.1.2)
2025-07-23 10:25:17 | INFO | utils.device_discovery:discover_all_devices:305 | 设备发现完成，共发现 1 个设备
2025-07-23 10:25:17 | INFO | core.base_driver:_discover_and_update_current_device:333 | 发现设备: TECNO CM8 (13764254B4001229)
2025-07-23 10:25:17 | INFO | core.base_driver:_discover_and_update_current_device:344 | 设备已存在于配置中: current_device
2025-07-23 10:25:17 | INFO | core.base_driver:_discover_and_update_current_device:350 | ✅ 设备 'current_device' 已是当前设备
2025-07-23 10:25:17 | INFO | core.base_driver:_load_device_config:98 | 加载设备配置: current_device
2025-07-23 10:25:17 | INFO | core.base_driver:_load_device_config:99 |   设备名称: TECNO CM8
2025-07-23 10:25:17 | INFO | core.base_driver:_load_device_config:100 |   设备ID: 13764254B4001229
2025-07-23 10:25:17 | INFO | core.base_driver:_load_device_config:101 |   HiOS版本: 15.0.3
2025-07-23 10:25:17 | INFO | core.base_driver:_connect_device:43 | 正在连接指定设备: TECNO CM8 (13764254B4001229)
2025-07-23 10:25:17 | INFO | core.base_driver:_ensure_uiautomator2_service:147 | 检查UIAutomator2服务状态...
2025-07-23 10:25:17 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 13764254B4001229)
2025-07-23 10:25:18 | INFO | utils.uiautomator2_manager:check_service_health:53 | ✅ UIAutomator2服务健康状态良好
2025-07-23 10:25:18 | INFO | core.base_driver:_ensure_uiautomator2_service:154 | ✅ UIAutomator2服务状态正常
2025-07-23 10:25:19 | INFO | core.base_driver:_connect_device:53 | 设备连接成功: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-23 10:25:19 | INFO | core.base_driver:_verify_device_info:134 | ✅ 设备验证通过
2025-07-23 10:25:19 | INFO | core.base_driver:_setup_implicit_wait:189 | 设置隐式等待时间: 10秒
2025-07-23 10:25:19 | INFO | pages.apps.ella.ella_status_checker:check_flashlight_status:127 | 检查手电筒状态
2025-07-23 10:25:19 | INFO | pages.apps.ella.ella_status_checker:check_flashlight_status:154 | 手电筒状态: 开启 (通过media.camera检测到最近开启: 07-22 22:25:54 : Torch for camera id 0 turned on for client PID 11710)
2025-07-23 10:25:19 | INFO | pages.apps.ella.ella_status_checker:get_flashlight_detailed_status:282 | 获取手电筒详细状态
2025-07-23 10:25:19 | INFO | pages.apps.ella.ella_status_checker:check_flashlight_status:127 | 检查手电筒状态
2025-07-23 10:25:19 | INFO | pages.apps.ella.ella_status_checker:check_flashlight_status:154 | 手电筒状态: 开启 (通过media.camera检测到最近开启: 07-22 22:25:54 : Torch for camera id 0 turned on for client PID 11710)
2025-07-23 10:25:35 | INFO | core.base_driver:_discover_and_update_current_device:319 | 🔍 主动识别当前连接的设备...
2025-07-23 10:25:35 | INFO | utils.device_discovery:discover_all_devices:288 | 开始发现所有连接的设备...
2025-07-23 10:25:35 | INFO | utils.device_discovery:get_connected_devices:49 | 发现 1 个连接的设备: ['13764254B4001229']
2025-07-23 10:25:35 | INFO | utils.device_discovery:discover_device:243 | 正在发现设备: 13764254B4001229
2025-07-23 10:25:37 | INFO | utils.device_discovery:extract_hios_version:187 | 提取到HiOS版本: 15.1.2 (来源: CM8-15.1.2.017(OP001PF001AZ)_SU)
2025-07-23 10:25:37 | INFO | utils.device_discovery:discover_device:274 | 设备发现完成: TECNO CM8 (HiOS: 15.1.2)
2025-07-23 10:25:37 | INFO | utils.device_discovery:discover_all_devices:305 | 设备发现完成，共发现 1 个设备
2025-07-23 10:25:37 | INFO | core.base_driver:_discover_and_update_current_device:333 | 发现设备: TECNO CM8 (13764254B4001229)
2025-07-23 10:25:37 | INFO | core.base_driver:_discover_and_update_current_device:344 | 设备已存在于配置中: current_device
2025-07-23 10:25:37 | INFO | core.base_driver:_discover_and_update_current_device:350 | ✅ 设备 'current_device' 已是当前设备
2025-07-23 10:25:37 | INFO | core.base_driver:_load_device_config:98 | 加载设备配置: current_device
2025-07-23 10:25:37 | INFO | core.base_driver:_load_device_config:99 |   设备名称: TECNO CM8
2025-07-23 10:25:37 | INFO | core.base_driver:_load_device_config:100 |   设备ID: 13764254B4001229
2025-07-23 10:25:37 | INFO | core.base_driver:_load_device_config:101 |   HiOS版本: 15.0.3
2025-07-23 10:25:37 | INFO | core.base_driver:_connect_device:43 | 正在连接指定设备: TECNO CM8 (13764254B4001229)
2025-07-23 10:25:37 | INFO | core.base_driver:_ensure_uiautomator2_service:147 | 检查UIAutomator2服务状态...
2025-07-23 10:25:37 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 13764254B4001229)
2025-07-23 10:25:38 | INFO | utils.uiautomator2_manager:check_service_health:53 | ✅ UIAutomator2服务健康状态良好
2025-07-23 10:25:38 | INFO | core.base_driver:_ensure_uiautomator2_service:154 | ✅ UIAutomator2服务状态正常
2025-07-23 10:25:39 | INFO | core.base_driver:_connect_device:53 | 设备连接成功: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-23 10:25:39 | INFO | core.base_driver:_verify_device_info:134 | ✅ 设备验证通过
2025-07-23 10:25:39 | INFO | core.base_driver:_setup_implicit_wait:189 | 设置隐式等待时间: 10秒
2025-07-23 10:25:39 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-07-23 10:25:39 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-07-23 10:25:39 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-07-23 10:25:39 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-23 10:25:39 | INFO | core.base_page:__init__:38 | 初始化页面: ella - main_page
2025-07-23 10:25:39 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:121 | 启动Ella应用（指定Activity）
2025-07-23 10:25:39 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:129 | 尝试启动Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-23 10:25:43 | INFO | pages.apps.ella.ella_status_checker:check_app_started:629 | ✅ 应用已在前台: com.transsion.aivoiceassistant
2025-07-23 10:25:43 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:134 | ✅ Ella应用启动成功（指定Activity）
2025-07-23 10:25:43 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:169 | 等待Ella页面加载完成，超时时间: 15秒
2025-07-23 10:25:43 | INFO | pages.apps.ella.ella_status_checker:check_service_health:586 | 检查UIAutomator2服务健康状态
2025-07-23 10:25:43 | INFO | pages.apps.ella.ella_status_checker:check_service_health:602 | ✅ UIAutomator2服务健康状态良好
2025-07-23 10:25:43 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:178 | 当前应用包名: com.transsion.aivoiceassistant
2025-07-23 10:25:43 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:181 | ✅ Ella应用包已确认
2025-07-23 10:25:43 | INFO | pages.apps.ella.main_page_refactored:_wait_for_ui_elements:203 | 等待Ella页面UI元素加载...
2025-07-23 10:25:44 | INFO | pages.apps.ella.main_page_refactored:_wait_for_ui_elements:224 | ✅ 检测到页面元素: 输入框
2025-07-23 10:25:45 | INFO | testcases.test_ella.base_ella_test:ella_app:26 | ✅ Ella应用启动成功
2025-07-23 10:25:45 | INFO | pages.apps.ella.ella_status_checker:check_flashlight_status:127 | 检查手电筒状态
2025-07-23 10:25:45 | INFO | pages.apps.ella.ella_status_checker:check_flashlight_status:154 | 手电筒状态: 开启 (通过media.camera检测到最近开启: 07-22 22:25:54 : Torch for camera id 0 turned on for client PID 11710)
2025-07-23 10:25:45 | INFO | testcases.test_ella.base_ella_test:execute_command_and_verify:53 | 初始状态 - 使用命令open flashlight，状态: True
2025-07-23 10:25:45 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:253 | 确保在对话页面...
2025-07-23 10:25:45 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:547 | 检查当前进程是否是Ella...
2025-07-23 10:25:45 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:554 | 当前应用: com.transsion.aivoiceassistant
2025-07-23 10:25:45 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:555 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-23 10:25:45 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:564 | ✅ 当前在Ella应用进程
2025-07-23 10:25:46 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:264 | ✅ 已在对话页面
2025-07-23 10:25:46 | INFO | pages.apps.ella.ella_command_executor:_ensure_input_box_ready:130 | 确保输入框就绪...
2025-07-23 10:25:46 | INFO | pages.apps.ella.ella_command_executor:_check_known_input_elements:160 | ✅ 找到主输入框
2025-07-23 10:25:46 | INFO | pages.apps.ella.ella_command_executor:execute_text_command:34 | 执行文本命令: open flashlight
2025-07-23 10:25:46 | INFO | pages.apps.ella.ella_command_executor:_ensure_input_box_ready:130 | 确保输入框就绪...
2025-07-23 10:25:46 | INFO | pages.apps.ella.ella_command_executor:_check_known_input_elements:160 | ✅ 找到主输入框
2025-07-23 10:25:46 | INFO | pages.apps.ella.ella_command_executor:_input_text_command:230 | 输入文本命令: open flashlight
2025-07-23 10:25:46 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-23 10:25:46 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-23 10:25:46 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-23 10:25:47 | INFO | core.base_element:clear_text:325 | 清空文本成功 [输入框]
2025-07-23 10:25:47 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-23 10:25:47 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-23 10:25:47 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-23 10:25:47 | INFO | core.base_element:send_keys:302 | 输入文本成功 [输入框]: open flashlight
2025-07-23 10:25:47 | INFO | pages.apps.ella.ella_command_executor:_input_text_command:247 | ✅ 文本输入成功
2025-07-23 10:25:47 | INFO | pages.apps.ella.ella_command_executor:_send_command:321 | 发送命令
2025-07-23 10:25:48 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-07-23 10:25:48 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-23 10:25:48 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-07-23 10:25:49 | INFO | core.base_element:click:231 | 点击元素成功 [发送按钮]
2025-07-23 10:25:49 | INFO | pages.apps.ella.ella_command_executor:_send_command:327 | ✅ 点击发送按钮成功
2025-07-23 10:25:49 | INFO | pages.apps.ella.ella_command_executor:execute_text_command:53 | ✅ 文本命令执行完成
2025-07-23 10:25:49 | INFO | testcases.test_ella.base_ella_test:_execute_command:353 | ✅ 成功执行命令: open flashlight
2025-07-23 10:25:49 | INFO | pages.apps.ella.ella_response_handler:wait_for_response:33 | 等待AI响应，超时时间: 8秒
2025-07-23 10:25:50 | INFO | pages.apps.ella.ella_response_handler:wait_for_response:58 | ✅ 通过TTS按钮检测到响应
2025-07-23 10:25:53 | INFO | pages.apps.ella.ella_status_checker:check_flashlight_status:127 | 检查手电筒状态
2025-07-23 10:25:53 | INFO | pages.apps.ella.ella_status_checker:check_flashlight_status:154 | 手电筒状态: 开启 (通过media.camera检测到最近开启: 07-22 22:25:54 : Torch for camera id 0 turned on for client PID 11710)
2025-07-23 10:25:53 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:90 | 状态检查完成，现在获取响应文本
2025-07-23 10:25:53 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:94 | 第1次尝试确保在Ella页面以获取响应
2025-07-23 10:25:53 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:253 | 确保在对话页面...
2025-07-23 10:25:53 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:547 | 检查当前进程是否是Ella...
2025-07-23 10:25:53 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:554 | 当前应用: com.transsion.aivoiceassistant
2025-07-23 10:25:53 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:555 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-23 10:25:53 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:564 | ✅ 当前在Ella应用进程
2025-07-23 10:25:53 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:264 | ✅ 已在对话页面
2025-07-23 10:25:53 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:98 | ✅ 已确认在Ella对话页面，可以获取响应
2025-07-23 10:25:53 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:547 | 检查当前进程是否是Ella...
2025-07-23 10:25:54 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:554 | 当前应用: com.transsion.aivoiceassistant
2025-07-23 10:25:54 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:555 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-23 10:25:54 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:564 | ✅ 当前在Ella应用进程
2025-07-23 10:25:54 | INFO | pages.apps.ella.ella_response_handler:get_response_all_text:129 | 获取AI响应文本
2025-07-23 10:25:56 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:290 | asr_txt文本不符合AI响应格式: open flashlight，已达到最大重试次数
2025-07-23 10:25:57 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:290 | robot_text文本不符合AI响应格式: Flashlight is turned on now.，已达到最大重试次数
2025-07-23 10:25:59 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:290 | function_name文本不符合AI响应格式: Flashlight，已达到最大重试次数
2025-07-23 10:26:01 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:265 | function_control节点文本为空，已达到最大重试次数
2025-07-23 10:26:01 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:119 | 最终获取的AI响应: '['open flashlight', 'Flashlight is turned on now.', 'Flashlight', '']'
2025-07-23 10:26:01 | INFO | testcases.test_ella.base_ella_test:_verify_status_change:453 | ✅ 状态验证通过: True -> True
2025-07-23 10:26:01 | INFO | testcases.test_ella.base_ella_test:simple_command_test:678 | 🎉 open flashlight 测试完成
2025-07-23 10:26:01 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:490 | verify_expected_in_response 响应类型: <class 'list'>, 内容: ['open flashlight', 'Flashlight is turned on now.', 'Flashlight', '']
2025-07-23 10:26:01 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:534 | ✅ 响应包含期望内容: 'flashlight'
2025-07-23 10:26:01 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:542 | 🎉 所有期望内容都已找到 (1/1)
2025-07-23 10:26:01 | INFO | core.base_page:stop_app:91 | 停止应用: ella (com.transsion.aivoiceassistant)
2025-07-23 10:26:01 | INFO | core.base_driver:stop_app:247 | 停止应用: com.transsion.aivoiceassistant
2025-07-23 10:26:02 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-07-23 10:26:02 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-07-23 10:26:02 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
2025-07-23 10:27:58 | INFO | core.base_driver:_discover_and_update_current_device:319 | 🔍 主动识别当前连接的设备...
2025-07-23 10:27:58 | INFO | utils.device_discovery:discover_all_devices:288 | 开始发现所有连接的设备...
2025-07-23 10:27:58 | INFO | utils.device_discovery:get_connected_devices:49 | 发现 1 个连接的设备: ['13764254B4001229']
2025-07-23 10:27:58 | INFO | utils.device_discovery:discover_device:243 | 正在发现设备: 13764254B4001229
2025-07-23 10:28:01 | INFO | utils.device_discovery:extract_hios_version:187 | 提取到HiOS版本: 15.1.2 (来源: CM8-15.1.2.017(OP001PF001AZ)_SU)
2025-07-23 10:28:01 | INFO | utils.device_discovery:discover_device:274 | 设备发现完成: TECNO CM8 (HiOS: 15.1.2)
2025-07-23 10:28:01 | INFO | utils.device_discovery:discover_all_devices:305 | 设备发现完成，共发现 1 个设备
2025-07-23 10:28:01 | INFO | core.base_driver:_discover_and_update_current_device:333 | 发现设备: TECNO CM8 (13764254B4001229)
2025-07-23 10:28:01 | INFO | core.base_driver:_discover_and_update_current_device:344 | 设备已存在于配置中: current_device
2025-07-23 10:28:01 | INFO | core.base_driver:_discover_and_update_current_device:350 | ✅ 设备 'current_device' 已是当前设备
2025-07-23 10:28:01 | INFO | core.base_driver:_load_device_config:98 | 加载设备配置: current_device
2025-07-23 10:28:01 | INFO | core.base_driver:_load_device_config:99 |   设备名称: TECNO CM8
2025-07-23 10:28:01 | INFO | core.base_driver:_load_device_config:100 |   设备ID: 13764254B4001229
2025-07-23 10:28:01 | INFO | core.base_driver:_load_device_config:101 |   HiOS版本: 15.0.3
2025-07-23 10:28:01 | INFO | core.base_driver:_connect_device:43 | 正在连接指定设备: TECNO CM8 (13764254B4001229)
2025-07-23 10:28:01 | INFO | core.base_driver:_ensure_uiautomator2_service:147 | 检查UIAutomator2服务状态...
2025-07-23 10:28:01 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 13764254B4001229)
2025-07-23 10:28:01 | INFO | utils.uiautomator2_manager:check_service_health:53 | ✅ UIAutomator2服务健康状态良好
2025-07-23 10:28:01 | INFO | core.base_driver:_ensure_uiautomator2_service:154 | ✅ UIAutomator2服务状态正常
2025-07-23 10:28:02 | INFO | core.base_driver:_connect_device:53 | 设备连接成功: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-23 10:28:02 | INFO | core.base_driver:_verify_device_info:134 | ✅ 设备验证通过
2025-07-23 10:28:02 | INFO | core.base_driver:_setup_implicit_wait:189 | 设置隐式等待时间: 10秒
2025-07-23 10:28:02 | INFO | __main__:check_flashlight_status:127 | 检查手电筒状态
2025-07-23 10:28:02 | INFO | __main__:check_flashlight_status:154 | 手电筒状态: 开启 (通过media.camera检测到最近开启: 07-22 22:25:54 : Torch for camera id 0 turned on for client PID 11710)
2025-07-23 10:28:26 | INFO | core.base_driver:_discover_and_update_current_device:319 | 🔍 主动识别当前连接的设备...
2025-07-23 10:28:26 | INFO | utils.device_discovery:discover_all_devices:288 | 开始发现所有连接的设备...
2025-07-23 10:28:26 | INFO | utils.device_discovery:get_connected_devices:49 | 发现 1 个连接的设备: ['13764254B4001229']
2025-07-23 10:28:26 | INFO | utils.device_discovery:discover_device:243 | 正在发现设备: 13764254B4001229
2025-07-23 10:28:29 | INFO | utils.device_discovery:extract_hios_version:187 | 提取到HiOS版本: 15.1.2 (来源: CM8-15.1.2.017(OP001PF001AZ)_SU)
2025-07-23 10:28:29 | INFO | utils.device_discovery:discover_device:274 | 设备发现完成: TECNO CM8 (HiOS: 15.1.2)
2025-07-23 10:28:29 | INFO | utils.device_discovery:discover_all_devices:305 | 设备发现完成，共发现 1 个设备
2025-07-23 10:28:29 | INFO | core.base_driver:_discover_and_update_current_device:333 | 发现设备: TECNO CM8 (13764254B4001229)
2025-07-23 10:28:29 | INFO | core.base_driver:_discover_and_update_current_device:344 | 设备已存在于配置中: current_device
2025-07-23 10:28:29 | INFO | core.base_driver:_discover_and_update_current_device:350 | ✅ 设备 'current_device' 已是当前设备
2025-07-23 10:28:29 | INFO | core.base_driver:_load_device_config:98 | 加载设备配置: current_device
2025-07-23 10:28:29 | INFO | core.base_driver:_load_device_config:99 |   设备名称: TECNO CM8
2025-07-23 10:28:29 | INFO | core.base_driver:_load_device_config:100 |   设备ID: 13764254B4001229
2025-07-23 10:28:29 | INFO | core.base_driver:_load_device_config:101 |   HiOS版本: 15.0.3
2025-07-23 10:28:29 | INFO | core.base_driver:_connect_device:43 | 正在连接指定设备: TECNO CM8 (13764254B4001229)
2025-07-23 10:28:29 | INFO | core.base_driver:_ensure_uiautomator2_service:147 | 检查UIAutomator2服务状态...
2025-07-23 10:28:29 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 13764254B4001229)
2025-07-23 10:28:29 | INFO | utils.uiautomator2_manager:check_service_health:53 | ✅ UIAutomator2服务健康状态良好
2025-07-23 10:28:29 | INFO | core.base_driver:_ensure_uiautomator2_service:154 | ✅ UIAutomator2服务状态正常
2025-07-23 10:28:30 | INFO | core.base_driver:_connect_device:53 | 设备连接成功: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-23 10:28:30 | INFO | core.base_driver:_verify_device_info:134 | ✅ 设备验证通过
2025-07-23 10:28:30 | INFO | core.base_driver:_setup_implicit_wait:189 | 设置隐式等待时间: 10秒
2025-07-23 10:28:30 | INFO | __main__:check_flashlight_status:127 | 检查手电筒状态
2025-07-23 10:28:30 | INFO | __main__:check_flashlight_status:154 | 手电筒状态: 开启 (通过media.camera检测到最近开启: 07-22 22:25:54 : Torch for camera id 0 turned on for client PID 11710)
2025-07-23 10:28:34 | INFO | core.base_driver:_discover_and_update_current_device:319 | 🔍 主动识别当前连接的设备...
2025-07-23 10:28:34 | INFO | utils.device_discovery:discover_all_devices:288 | 开始发现所有连接的设备...
2025-07-23 10:28:34 | INFO | utils.device_discovery:get_connected_devices:49 | 发现 1 个连接的设备: ['13764254B4001229']
2025-07-23 10:28:34 | INFO | utils.device_discovery:discover_device:243 | 正在发现设备: 13764254B4001229
2025-07-23 10:28:36 | INFO | utils.device_discovery:extract_hios_version:187 | 提取到HiOS版本: 15.1.2 (来源: CM8-15.1.2.017(OP001PF001AZ)_SU)
2025-07-23 10:28:36 | INFO | utils.device_discovery:discover_device:274 | 设备发现完成: TECNO CM8 (HiOS: 15.1.2)
2025-07-23 10:28:36 | INFO | utils.device_discovery:discover_all_devices:305 | 设备发现完成，共发现 1 个设备
2025-07-23 10:28:36 | INFO | core.base_driver:_discover_and_update_current_device:333 | 发现设备: TECNO CM8 (13764254B4001229)
2025-07-23 10:28:36 | INFO | core.base_driver:_discover_and_update_current_device:344 | 设备已存在于配置中: current_device
2025-07-23 10:28:36 | INFO | core.base_driver:_discover_and_update_current_device:350 | ✅ 设备 'current_device' 已是当前设备
2025-07-23 10:28:36 | INFO | core.base_driver:_load_device_config:98 | 加载设备配置: current_device
2025-07-23 10:28:36 | INFO | core.base_driver:_load_device_config:99 |   设备名称: TECNO CM8
2025-07-23 10:28:36 | INFO | core.base_driver:_load_device_config:100 |   设备ID: 13764254B4001229
2025-07-23 10:28:36 | INFO | core.base_driver:_load_device_config:101 |   HiOS版本: 15.0.3
2025-07-23 10:28:36 | INFO | core.base_driver:_connect_device:43 | 正在连接指定设备: TECNO CM8 (13764254B4001229)
2025-07-23 10:28:36 | INFO | core.base_driver:_ensure_uiautomator2_service:147 | 检查UIAutomator2服务状态...
2025-07-23 10:28:36 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 13764254B4001229)
2025-07-23 10:28:37 | INFO | utils.uiautomator2_manager:check_service_health:53 | ✅ UIAutomator2服务健康状态良好
2025-07-23 10:28:37 | INFO | core.base_driver:_ensure_uiautomator2_service:154 | ✅ UIAutomator2服务状态正常
2025-07-23 10:28:38 | INFO | core.base_driver:_connect_device:53 | 设备连接成功: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-23 10:28:38 | INFO | core.base_driver:_verify_device_info:134 | ✅ 设备验证通过
2025-07-23 10:28:38 | INFO | core.base_driver:_setup_implicit_wait:189 | 设置隐式等待时间: 10秒
2025-07-23 10:28:38 | INFO | __main__:check_flashlight_status:127 | 检查手电筒状态
2025-07-23 10:28:38 | INFO | __main__:check_flashlight_status:154 | 手电筒状态: 开启 (通过media.camera检测到最近开启: 07-22 22:25:54 : Torch for camera id 0 turned on for client PID 11710)
2025-07-23 10:29:48 | INFO | core.base_driver:_discover_and_update_current_device:319 | 🔍 主动识别当前连接的设备...
2025-07-23 10:29:48 | INFO | utils.device_discovery:discover_all_devices:288 | 开始发现所有连接的设备...
2025-07-23 10:29:48 | INFO | utils.device_discovery:get_connected_devices:49 | 发现 1 个连接的设备: ['13764254B4001229']
2025-07-23 10:29:48 | INFO | utils.device_discovery:discover_device:243 | 正在发现设备: 13764254B4001229
2025-07-23 10:29:51 | INFO | utils.device_discovery:extract_hios_version:187 | 提取到HiOS版本: 15.1.2 (来源: CM8-15.1.2.017(OP001PF001AZ)_SU)
2025-07-23 10:29:51 | INFO | utils.device_discovery:discover_device:274 | 设备发现完成: TECNO CM8 (HiOS: 15.1.2)
2025-07-23 10:29:51 | INFO | utils.device_discovery:discover_all_devices:305 | 设备发现完成，共发现 1 个设备
2025-07-23 10:29:51 | INFO | core.base_driver:_discover_and_update_current_device:333 | 发现设备: TECNO CM8 (13764254B4001229)
2025-07-23 10:29:51 | INFO | core.base_driver:_discover_and_update_current_device:344 | 设备已存在于配置中: current_device
2025-07-23 10:29:51 | INFO | core.base_driver:_discover_and_update_current_device:350 | ✅ 设备 'current_device' 已是当前设备
2025-07-23 10:29:51 | INFO | core.base_driver:_load_device_config:98 | 加载设备配置: current_device
2025-07-23 10:29:51 | INFO | core.base_driver:_load_device_config:99 |   设备名称: TECNO CM8
2025-07-23 10:29:51 | INFO | core.base_driver:_load_device_config:100 |   设备ID: 13764254B4001229
2025-07-23 10:29:51 | INFO | core.base_driver:_load_device_config:101 |   HiOS版本: 15.0.3
2025-07-23 10:29:51 | INFO | core.base_driver:_connect_device:43 | 正在连接指定设备: TECNO CM8 (13764254B4001229)
2025-07-23 10:29:51 | INFO | core.base_driver:_ensure_uiautomator2_service:147 | 检查UIAutomator2服务状态...
2025-07-23 10:29:51 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 13764254B4001229)
2025-07-23 10:29:51 | INFO | utils.uiautomator2_manager:check_service_health:53 | ✅ UIAutomator2服务健康状态良好
2025-07-23 10:29:51 | INFO | core.base_driver:_ensure_uiautomator2_service:154 | ✅ UIAutomator2服务状态正常
2025-07-23 10:29:52 | INFO | core.base_driver:_connect_device:53 | 设备连接成功: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-23 10:29:52 | INFO | core.base_driver:_verify_device_info:134 | ✅ 设备验证通过
2025-07-23 10:29:52 | INFO | core.base_driver:_setup_implicit_wait:189 | 设置隐式等待时间: 10秒
2025-07-23 10:29:52 | INFO | pages.apps.ella.ella_status_checker:check_flashlight_status:127 | 检查手电筒状态
2025-07-23 10:29:52 | INFO | pages.apps.ella.ella_status_checker:check_flashlight_status:154 | 手电筒状态: 开启 (通过media.camera检测到最近开启: 07-22 22:25:54 : Torch for camera id 0 turned on for client PID 11710)
2025-07-23 10:29:52 | INFO | pages.apps.ella.ella_status_checker:get_flashlight_detailed_status:282 | 获取手电筒详细状态
2025-07-23 10:29:52 | INFO | pages.apps.ella.ella_status_checker:check_flashlight_status:127 | 检查手电筒状态
2025-07-23 10:29:52 | INFO | pages.apps.ella.ella_status_checker:check_flashlight_status:154 | 手电筒状态: 开启 (通过media.camera检测到最近开启: 07-22 22:25:54 : Torch for camera id 0 turned on for client PID 11710)
2025-07-23 10:30:32 | INFO | core.base_driver:_discover_and_update_current_device:319 | 🔍 主动识别当前连接的设备...
2025-07-23 10:30:32 | INFO | utils.device_discovery:discover_all_devices:288 | 开始发现所有连接的设备...
2025-07-23 10:30:33 | INFO | utils.device_discovery:get_connected_devices:49 | 发现 1 个连接的设备: ['13764254B4001229']
2025-07-23 10:30:33 | INFO | utils.device_discovery:discover_device:243 | 正在发现设备: 13764254B4001229
2025-07-23 10:30:35 | INFO | utils.device_discovery:extract_hios_version:187 | 提取到HiOS版本: 15.1.2 (来源: CM8-15.1.2.017(OP001PF001AZ)_SU)
2025-07-23 10:30:35 | INFO | utils.device_discovery:discover_device:274 | 设备发现完成: TECNO CM8 (HiOS: 15.1.2)
2025-07-23 10:30:35 | INFO | utils.device_discovery:discover_all_devices:305 | 设备发现完成，共发现 1 个设备
2025-07-23 10:30:35 | INFO | core.base_driver:_discover_and_update_current_device:333 | 发现设备: TECNO CM8 (13764254B4001229)
2025-07-23 10:30:35 | INFO | core.base_driver:_discover_and_update_current_device:344 | 设备已存在于配置中: current_device
2025-07-23 10:30:35 | INFO | core.base_driver:_discover_and_update_current_device:350 | ✅ 设备 'current_device' 已是当前设备
2025-07-23 10:30:35 | INFO | core.base_driver:_load_device_config:98 | 加载设备配置: current_device
2025-07-23 10:30:35 | INFO | core.base_driver:_load_device_config:99 |   设备名称: TECNO CM8
2025-07-23 10:30:35 | INFO | core.base_driver:_load_device_config:100 |   设备ID: 13764254B4001229
2025-07-23 10:30:35 | INFO | core.base_driver:_load_device_config:101 |   HiOS版本: 15.0.3
2025-07-23 10:30:35 | INFO | core.base_driver:_connect_device:43 | 正在连接指定设备: TECNO CM8 (13764254B4001229)
2025-07-23 10:30:35 | INFO | core.base_driver:_ensure_uiautomator2_service:147 | 检查UIAutomator2服务状态...
2025-07-23 10:30:35 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 13764254B4001229)
2025-07-23 10:30:36 | INFO | utils.uiautomator2_manager:check_service_health:53 | ✅ UIAutomator2服务健康状态良好
2025-07-23 10:30:36 | INFO | core.base_driver:_ensure_uiautomator2_service:154 | ✅ UIAutomator2服务状态正常
2025-07-23 10:30:37 | INFO | core.base_driver:_connect_device:53 | 设备连接成功: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-23 10:30:37 | INFO | core.base_driver:_verify_device_info:134 | ✅ 设备验证通过
2025-07-23 10:30:37 | INFO | core.base_driver:_setup_implicit_wait:189 | 设置隐式等待时间: 10秒
2025-07-23 10:30:37 | INFO | pages.apps.ella.ella_status_checker:check_flashlight_status:127 | 检查手电筒状态
2025-07-23 10:30:37 | INFO | pages.apps.ella.ella_status_checker:check_flashlight_status:154 | 手电筒状态: 开启 (通过media.camera检测到最近开启: 07-22 22:25:54 : Torch for camera id 0 turned on for client PID 11710)
2025-07-23 10:30:37 | INFO | pages.apps.ella.ella_status_checker:get_flashlight_detailed_status:291 | 获取手电筒详细状态
2025-07-23 10:30:37 | INFO | pages.apps.ella.ella_status_checker:check_flashlight_status:127 | 检查手电筒状态
2025-07-23 10:30:37 | INFO | pages.apps.ella.ella_status_checker:check_flashlight_status:154 | 手电筒状态: 开启 (通过media.camera检测到最近开启: 07-22 22:25:54 : Torch for camera id 0 turned on for client PID 11710)
2025-07-23 10:31:18 | INFO | core.base_driver:_discover_and_update_current_device:319 | 🔍 主动识别当前连接的设备...
2025-07-23 10:31:18 | INFO | utils.device_discovery:discover_all_devices:288 | 开始发现所有连接的设备...
2025-07-23 10:31:18 | INFO | utils.device_discovery:get_connected_devices:49 | 发现 1 个连接的设备: ['13764254B4001229']
2025-07-23 10:31:18 | INFO | utils.device_discovery:discover_device:243 | 正在发现设备: 13764254B4001229
2025-07-23 10:31:21 | INFO | utils.device_discovery:extract_hios_version:187 | 提取到HiOS版本: 15.1.2 (来源: CM8-15.1.2.017(OP001PF001AZ)_SU)
2025-07-23 10:31:21 | INFO | utils.device_discovery:discover_device:274 | 设备发现完成: TECNO CM8 (HiOS: 15.1.2)
2025-07-23 10:31:21 | INFO | utils.device_discovery:discover_all_devices:305 | 设备发现完成，共发现 1 个设备
2025-07-23 10:31:21 | INFO | core.base_driver:_discover_and_update_current_device:333 | 发现设备: TECNO CM8 (13764254B4001229)
2025-07-23 10:31:21 | INFO | core.base_driver:_discover_and_update_current_device:344 | 设备已存在于配置中: current_device
2025-07-23 10:31:21 | INFO | core.base_driver:_discover_and_update_current_device:350 | ✅ 设备 'current_device' 已是当前设备
2025-07-23 10:31:21 | INFO | core.base_driver:_load_device_config:98 | 加载设备配置: current_device
2025-07-23 10:31:21 | INFO | core.base_driver:_load_device_config:99 |   设备名称: TECNO CM8
2025-07-23 10:31:21 | INFO | core.base_driver:_load_device_config:100 |   设备ID: 13764254B4001229
2025-07-23 10:31:21 | INFO | core.base_driver:_load_device_config:101 |   HiOS版本: 15.0.3
2025-07-23 10:31:21 | INFO | core.base_driver:_connect_device:43 | 正在连接指定设备: TECNO CM8 (13764254B4001229)
2025-07-23 10:31:21 | INFO | core.base_driver:_ensure_uiautomator2_service:147 | 检查UIAutomator2服务状态...
2025-07-23 10:31:21 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 13764254B4001229)
2025-07-23 10:31:22 | INFO | utils.uiautomator2_manager:check_service_health:53 | ✅ UIAutomator2服务健康状态良好
2025-07-23 10:31:22 | INFO | core.base_driver:_ensure_uiautomator2_service:154 | ✅ UIAutomator2服务状态正常
2025-07-23 10:31:22 | INFO | core.base_driver:_connect_device:53 | 设备连接成功: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-23 10:31:22 | INFO | core.base_driver:_verify_device_info:134 | ✅ 设备验证通过
2025-07-23 10:31:22 | INFO | core.base_driver:_setup_implicit_wait:189 | 设置隐式等待时间: 10秒
2025-07-23 10:31:22 | INFO | pages.apps.ella.ella_status_checker:check_flashlight_status:127 | 检查手电筒状态
2025-07-23 10:31:22 | INFO | pages.apps.ella.ella_status_checker:check_flashlight_status:154 | 手电筒状态: 开启 (通过media.camera检测到最近开启: 07-22 22:25:54 : Torch for camera id 0 turned on for client PID 11710)
2025-07-23 10:31:22 | INFO | pages.apps.ella.ella_status_checker:get_flashlight_detailed_status:291 | 获取手电筒详细状态
2025-07-23 10:31:22 | INFO | pages.apps.ella.ella_status_checker:check_flashlight_status:127 | 检查手电筒状态
2025-07-23 10:31:22 | INFO | pages.apps.ella.ella_status_checker:check_flashlight_status:154 | 手电筒状态: 开启 (通过media.camera检测到最近开启: 07-22 22:25:54 : Torch for camera id 0 turned on for client PID 11710)
2025-07-23 10:31:56 | INFO | core.base_driver:_discover_and_update_current_device:319 | 🔍 主动识别当前连接的设备...
2025-07-23 10:31:56 | INFO | utils.device_discovery:discover_all_devices:288 | 开始发现所有连接的设备...
2025-07-23 10:31:56 | INFO | utils.device_discovery:get_connected_devices:49 | 发现 1 个连接的设备: ['13764254B4001229']
2025-07-23 10:31:56 | INFO | utils.device_discovery:discover_device:243 | 正在发现设备: 13764254B4001229
2025-07-23 10:31:58 | INFO | utils.device_discovery:extract_hios_version:187 | 提取到HiOS版本: 15.1.2 (来源: CM8-15.1.2.017(OP001PF001AZ)_SU)
2025-07-23 10:31:58 | INFO | utils.device_discovery:discover_device:274 | 设备发现完成: TECNO CM8 (HiOS: 15.1.2)
2025-07-23 10:31:58 | INFO | utils.device_discovery:discover_all_devices:305 | 设备发现完成，共发现 1 个设备
2025-07-23 10:31:58 | INFO | core.base_driver:_discover_and_update_current_device:333 | 发现设备: TECNO CM8 (13764254B4001229)
2025-07-23 10:31:58 | INFO | core.base_driver:_discover_and_update_current_device:344 | 设备已存在于配置中: current_device
2025-07-23 10:31:58 | INFO | core.base_driver:_discover_and_update_current_device:350 | ✅ 设备 'current_device' 已是当前设备
2025-07-23 10:31:58 | INFO | core.base_driver:_load_device_config:98 | 加载设备配置: current_device
2025-07-23 10:31:58 | INFO | core.base_driver:_load_device_config:99 |   设备名称: TECNO CM8
2025-07-23 10:31:58 | INFO | core.base_driver:_load_device_config:100 |   设备ID: 13764254B4001229
2025-07-23 10:31:58 | INFO | core.base_driver:_load_device_config:101 |   HiOS版本: 15.0.3
2025-07-23 10:31:58 | INFO | core.base_driver:_connect_device:43 | 正在连接指定设备: TECNO CM8 (13764254B4001229)
2025-07-23 10:31:58 | INFO | core.base_driver:_ensure_uiautomator2_service:147 | 检查UIAutomator2服务状态...
2025-07-23 10:31:58 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 13764254B4001229)
2025-07-23 10:31:59 | INFO | utils.uiautomator2_manager:check_service_health:53 | ✅ UIAutomator2服务健康状态良好
2025-07-23 10:31:59 | INFO | core.base_driver:_ensure_uiautomator2_service:154 | ✅ UIAutomator2服务状态正常
2025-07-23 10:31:59 | INFO | core.base_driver:_connect_device:53 | 设备连接成功: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-23 10:31:59 | INFO | core.base_driver:_verify_device_info:134 | ✅ 设备验证通过
2025-07-23 10:31:59 | INFO | core.base_driver:_setup_implicit_wait:189 | 设置隐式等待时间: 10秒
2025-07-23 10:31:59 | INFO | pages.apps.ella.ella_status_checker:check_flashlight_status:127 | 检查手电筒状态
2025-07-23 10:32:00 | INFO | pages.apps.ella.ella_status_checker:check_flashlight_status:157 | 手电筒状态: 关闭 (通过media.camera检测到最近关闭: 07-23 10:28:32 : Torch for camera id 0 turned off for client PID 2706)
2025-07-23 10:32:00 | INFO | pages.apps.ella.ella_status_checker:get_flashlight_detailed_status:291 | 获取手电筒详细状态
2025-07-23 10:32:00 | INFO | pages.apps.ella.ella_status_checker:check_flashlight_status:127 | 检查手电筒状态
2025-07-23 10:32:00 | INFO | pages.apps.ella.ella_status_checker:check_flashlight_status:157 | 手电筒状态: 关闭 (通过media.camera检测到最近关闭: 07-23 10:28:32 : Torch for camera id 0 turned off for client PID 2706)
2025-07-23 10:32:14 | INFO | core.base_driver:_discover_and_update_current_device:319 | 🔍 主动识别当前连接的设备...
2025-07-23 10:32:14 | INFO | utils.device_discovery:discover_all_devices:288 | 开始发现所有连接的设备...
2025-07-23 10:32:14 | INFO | utils.device_discovery:get_connected_devices:49 | 发现 1 个连接的设备: ['13764254B4001229']
2025-07-23 10:32:14 | INFO | utils.device_discovery:discover_device:243 | 正在发现设备: 13764254B4001229
2025-07-23 10:32:17 | INFO | utils.device_discovery:extract_hios_version:187 | 提取到HiOS版本: 15.1.2 (来源: CM8-15.1.2.017(OP001PF001AZ)_SU)
2025-07-23 10:32:17 | INFO | utils.device_discovery:discover_device:274 | 设备发现完成: TECNO CM8 (HiOS: 15.1.2)
2025-07-23 10:32:17 | INFO | utils.device_discovery:discover_all_devices:305 | 设备发现完成，共发现 1 个设备
2025-07-23 10:32:17 | INFO | core.base_driver:_discover_and_update_current_device:333 | 发现设备: TECNO CM8 (13764254B4001229)
2025-07-23 10:32:17 | INFO | core.base_driver:_discover_and_update_current_device:344 | 设备已存在于配置中: current_device
2025-07-23 10:32:17 | INFO | core.base_driver:_discover_and_update_current_device:350 | ✅ 设备 'current_device' 已是当前设备
2025-07-23 10:32:17 | INFO | core.base_driver:_load_device_config:98 | 加载设备配置: current_device
2025-07-23 10:32:17 | INFO | core.base_driver:_load_device_config:99 |   设备名称: TECNO CM8
2025-07-23 10:32:17 | INFO | core.base_driver:_load_device_config:100 |   设备ID: 13764254B4001229
2025-07-23 10:32:17 | INFO | core.base_driver:_load_device_config:101 |   HiOS版本: 15.0.3
2025-07-23 10:32:17 | INFO | core.base_driver:_connect_device:43 | 正在连接指定设备: TECNO CM8 (13764254B4001229)
2025-07-23 10:32:17 | INFO | core.base_driver:_ensure_uiautomator2_service:147 | 检查UIAutomator2服务状态...
2025-07-23 10:32:17 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 13764254B4001229)
2025-07-23 10:32:17 | INFO | utils.uiautomator2_manager:check_service_health:53 | ✅ UIAutomator2服务健康状态良好
2025-07-23 10:32:17 | INFO | core.base_driver:_ensure_uiautomator2_service:154 | ✅ UIAutomator2服务状态正常
2025-07-23 10:32:18 | INFO | core.base_driver:_connect_device:53 | 设备连接成功: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-23 10:32:18 | INFO | core.base_driver:_verify_device_info:134 | ✅ 设备验证通过
2025-07-23 10:32:18 | INFO | core.base_driver:_setup_implicit_wait:189 | 设置隐式等待时间: 10秒
2025-07-23 10:32:18 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-07-23 10:32:18 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-07-23 10:32:18 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-07-23 10:32:18 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-23 10:32:18 | INFO | core.base_page:__init__:38 | 初始化页面: ella - main_page
2025-07-23 10:32:19 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:121 | 启动Ella应用（指定Activity）
2025-07-23 10:32:19 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:129 | 尝试启动Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-23 10:32:22 | INFO | pages.apps.ella.ella_status_checker:check_app_started:638 | ✅ 应用已在前台: com.transsion.aivoiceassistant
2025-07-23 10:32:22 | INFO | pages.apps.ella.main_page_refactored:start_app_with_activity:134 | ✅ Ella应用启动成功（指定Activity）
2025-07-23 10:32:22 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:169 | 等待Ella页面加载完成，超时时间: 15秒
2025-07-23 10:32:22 | INFO | pages.apps.ella.ella_status_checker:check_service_health:595 | 检查UIAutomator2服务健康状态
2025-07-23 10:32:22 | INFO | pages.apps.ella.ella_status_checker:check_service_health:611 | ✅ UIAutomator2服务健康状态良好
2025-07-23 10:32:23 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:178 | 当前应用包名: com.transsion.aivoiceassistant
2025-07-23 10:32:23 | INFO | pages.apps.ella.main_page_refactored:wait_for_page_load:181 | ✅ Ella应用包已确认
2025-07-23 10:32:23 | INFO | pages.apps.ella.main_page_refactored:_wait_for_ui_elements:203 | 等待Ella页面UI元素加载...
2025-07-23 10:32:24 | INFO | pages.apps.ella.main_page_refactored:_wait_for_ui_elements:224 | ✅ 检测到页面元素: 聊天消息列表
2025-07-23 10:32:25 | INFO | testcases.test_ella.base_ella_test:ella_app:26 | ✅ Ella应用启动成功
2025-07-23 10:32:25 | INFO | pages.apps.ella.ella_status_checker:check_flashlight_status:127 | 检查手电筒状态
2025-07-23 10:32:25 | INFO | pages.apps.ella.ella_status_checker:check_flashlight_status:157 | 手电筒状态: 关闭 (通过media.camera检测到最近关闭: 07-23 10:28:32 : Torch for camera id 0 turned off for client PID 2706)
2025-07-23 10:32:25 | INFO | testcases.test_ella.base_ella_test:execute_command_and_verify:53 | 初始状态 - 使用命令open flashlight，状态: False
2025-07-23 10:32:25 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:253 | 确保在对话页面...
2025-07-23 10:32:25 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:556 | 检查当前进程是否是Ella...
2025-07-23 10:32:25 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:563 | 当前应用: com.transsion.aivoiceassistant
2025-07-23 10:32:25 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:564 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-23 10:32:25 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:573 | ✅ 当前在Ella应用进程
2025-07-23 10:32:27 | INFO | pages.apps.ella.main_page_refactored:_try_return_to_chat_page:346 | 按返回键回到主页...
2025-07-23 10:32:29 | INFO | pages.apps.ella.main_page_refactored:_try_return_to_chat_page:352 | ✅ 通过返回键回到对话页面
2025-07-23 10:32:29 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:269 | ✅ 成功返回到对话页面
2025-07-23 10:32:29 | INFO | pages.apps.ella.ella_command_executor:_ensure_input_box_ready:130 | 确保输入框就绪...
2025-07-23 10:32:29 | INFO | pages.apps.ella.ella_command_executor:_check_known_input_elements:160 | ✅ 找到主输入框
2025-07-23 10:32:29 | INFO | pages.apps.ella.ella_command_executor:execute_text_command:34 | 执行文本命令: open flashlight
2025-07-23 10:32:29 | INFO | pages.apps.ella.ella_command_executor:_ensure_input_box_ready:130 | 确保输入框就绪...
2025-07-23 10:32:29 | INFO | pages.apps.ella.ella_command_executor:_check_known_input_elements:160 | ✅ 找到主输入框
2025-07-23 10:32:29 | INFO | pages.apps.ella.ella_command_executor:_input_text_command:230 | 输入文本命令: open flashlight
2025-07-23 10:32:29 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-23 10:32:29 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-23 10:32:29 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-23 10:32:29 | INFO | core.base_element:clear_text:325 | 清空文本成功 [输入框]
2025-07-23 10:32:29 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [输入框], 超时时间: 5秒
2025-07-23 10:32:29 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-23 10:32:29 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [输入框]
2025-07-23 10:32:29 | INFO | core.base_element:send_keys:302 | 输入文本成功 [输入框]: open flashlight
2025-07-23 10:32:29 | INFO | pages.apps.ella.ella_command_executor:_input_text_command:247 | ✅ 文本输入成功
2025-07-23 10:32:29 | INFO | pages.apps.ella.ella_command_executor:_send_command:321 | 发送命令
2025-07-23 10:32:30 | INFO | core.base_element:wait_for_element:62 | 等待元素出现 [发送按钮], 超时时间: 5秒
2025-07-23 10:32:30 | INFO | core.base_element:wait_for_element:65 | 元素列表 [True]
2025-07-23 10:32:30 | INFO | core.base_element:wait_for_element:67 | 元素已出现 [发送按钮]
2025-07-23 10:32:30 | INFO | core.base_element:click:231 | 点击元素成功 [发送按钮]
2025-07-23 10:32:30 | INFO | pages.apps.ella.ella_command_executor:_send_command:327 | ✅ 点击发送按钮成功
2025-07-23 10:32:30 | INFO | pages.apps.ella.ella_command_executor:execute_text_command:53 | ✅ 文本命令执行完成
2025-07-23 10:32:30 | INFO | testcases.test_ella.base_ella_test:_execute_command:353 | ✅ 成功执行命令: open flashlight
2025-07-23 10:32:30 | INFO | pages.apps.ella.ella_response_handler:wait_for_response:33 | 等待AI响应，超时时间: 8秒
2025-07-23 10:32:30 | INFO | pages.apps.ella.ella_response_handler:wait_for_response:48 | ✅ 通过元素数量变化检测到响应
2025-07-23 10:32:33 | INFO | pages.apps.ella.ella_status_checker:check_flashlight_status:127 | 检查手电筒状态
2025-07-23 10:32:34 | INFO | pages.apps.ella.ella_status_checker:check_flashlight_status:154 | 手电筒状态: 开启 (通过media.camera检测到最近开启: 07-23 10:32:32 : Torch for camera id 0 turned on for client PID 17830)
2025-07-23 10:32:34 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:90 | 状态检查完成，现在获取响应文本
2025-07-23 10:32:34 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:94 | 第1次尝试确保在Ella页面以获取响应
2025-07-23 10:32:34 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:253 | 确保在对话页面...
2025-07-23 10:32:34 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:556 | 检查当前进程是否是Ella...
2025-07-23 10:32:34 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:563 | 当前应用: com.transsion.aivoiceassistant
2025-07-23 10:32:34 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:564 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-23 10:32:34 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:573 | ✅ 当前在Ella应用进程
2025-07-23 10:32:34 | INFO | pages.apps.ella.main_page_refactored:ensure_on_chat_page:264 | ✅ 已在对话页面
2025-07-23 10:32:34 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:98 | ✅ 已确认在Ella对话页面，可以获取响应
2025-07-23 10:32:34 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:556 | 检查当前进程是否是Ella...
2025-07-23 10:32:34 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:563 | 当前应用: com.transsion.aivoiceassistant
2025-07-23 10:32:34 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:564 | 当前Activity: com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity
2025-07-23 10:32:34 | INFO | pages.apps.ella.ella_status_checker:ensure_ella_process:573 | ✅ 当前在Ella应用进程
2025-07-23 10:32:34 | INFO | pages.apps.ella.ella_response_handler:get_response_all_text:129 | 获取AI响应文本
2025-07-23 10:32:36 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:290 | asr_txt文本不符合AI响应格式: open flashlight，已达到最大重试次数
2025-07-23 10:32:38 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:290 | robot_text文本不符合AI响应格式: Flashlight is turned on now.，已达到最大重试次数
2025-07-23 10:32:40 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:290 | function_name文本不符合AI响应格式: Flashlight，已达到最大重试次数
2025-07-23 10:32:41 | WARNING | pages.apps.ella.ella_response_handler:_get_element_text_with_retry:265 | function_control节点文本为空，已达到最大重试次数
2025-07-23 10:32:41 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:119 | 最终获取的AI响应: '['open flashlight', 'Flashlight is turned on now.', 'Flashlight', '']'
2025-07-23 10:32:41 | INFO | testcases.test_ella.base_ella_test:_verify_status_change:453 | ✅ 状态验证通过: False -> True
2025-07-23 10:32:42 | INFO | testcases.test_ella.base_ella_test:simple_command_test:678 | 🎉 open flashlight 测试完成
2025-07-23 10:32:42 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:490 | verify_expected_in_response 响应类型: <class 'list'>, 内容: ['open flashlight', 'Flashlight is turned on now.', 'Flashlight', '']
2025-07-23 10:32:42 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:534 | ✅ 响应包含期望内容: 'flashlight'
2025-07-23 10:32:42 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:542 | 🎉 所有期望内容都已找到 (1/1)
2025-07-23 10:32:42 | INFO | core.base_page:stop_app:91 | 停止应用: ella (com.transsion.aivoiceassistant)
2025-07-23 10:32:42 | INFO | core.base_driver:stop_app:247 | 停止应用: com.transsion.aivoiceassistant
2025-07-23 10:32:42 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-07-23 10:32:42 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-07-23 10:32:42 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
2025-07-23 10:33:28 | INFO | core.base_driver:_discover_and_update_current_device:319 | 🔍 主动识别当前连接的设备...
2025-07-23 10:33:28 | INFO | utils.device_discovery:discover_all_devices:288 | 开始发现所有连接的设备...
2025-07-23 10:33:28 | INFO | utils.device_discovery:get_connected_devices:49 | 发现 1 个连接的设备: ['13764254B4001229']
2025-07-23 10:33:28 | INFO | utils.device_discovery:discover_device:243 | 正在发现设备: 13764254B4001229
2025-07-23 10:33:31 | INFO | utils.device_discovery:extract_hios_version:187 | 提取到HiOS版本: 15.1.2 (来源: CM8-15.1.2.017(OP001PF001AZ)_SU)
2025-07-23 10:33:31 | INFO | utils.device_discovery:discover_device:274 | 设备发现完成: TECNO CM8 (HiOS: 15.1.2)
2025-07-23 10:33:31 | INFO | utils.device_discovery:discover_all_devices:305 | 设备发现完成，共发现 1 个设备
2025-07-23 10:33:31 | INFO | core.base_driver:_discover_and_update_current_device:333 | 发现设备: TECNO CM8 (13764254B4001229)
2025-07-23 10:33:31 | INFO | core.base_driver:_discover_and_update_current_device:344 | 设备已存在于配置中: current_device
2025-07-23 10:33:31 | INFO | core.base_driver:_discover_and_update_current_device:350 | ✅ 设备 'current_device' 已是当前设备
2025-07-23 10:33:31 | INFO | core.base_driver:_load_device_config:98 | 加载设备配置: current_device
2025-07-23 10:33:31 | INFO | core.base_driver:_load_device_config:99 |   设备名称: TECNO CM8
2025-07-23 10:33:31 | INFO | core.base_driver:_load_device_config:100 |   设备ID: 13764254B4001229
2025-07-23 10:33:31 | INFO | core.base_driver:_load_device_config:101 |   HiOS版本: 15.0.3
2025-07-23 10:33:31 | INFO | core.base_driver:_connect_device:43 | 正在连接指定设备: TECNO CM8 (13764254B4001229)
2025-07-23 10:33:31 | INFO | core.base_driver:_ensure_uiautomator2_service:147 | 检查UIAutomator2服务状态...
2025-07-23 10:33:31 | INFO | utils.uiautomator2_manager:check_service_health:31 | 检查UIAutomator2服务健康状态 (设备: 13764254B4001229)
2025-07-23 10:33:32 | INFO | utils.uiautomator2_manager:check_service_health:53 | ✅ UIAutomator2服务健康状态良好
2025-07-23 10:33:32 | INFO | core.base_driver:_ensure_uiautomator2_service:154 | ✅ UIAutomator2服务状态正常
2025-07-23 10:33:32 | INFO | core.base_driver:_connect_device:53 | 设备连接成功: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-23 10:33:32 | INFO | core.base_driver:_verify_device_info:134 | ✅ 设备验证通过
2025-07-23 10:33:32 | INFO | core.base_driver:_setup_implicit_wait:189 | 设置隐式等待时间: 10秒
2025-07-23 10:33:32 | INFO | __main__:check_flashlight_status:127 | 检查手电筒状态
2025-07-23 10:33:32 | INFO | __main__:check_flashlight_status:154 | 手电筒状态: 开启 (通过media.camera检测到最近开启: 07-23 10:32:32 : Torch for camera id 0 turned on for client PID 17830)
