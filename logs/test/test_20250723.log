2025-07-23 10:16:55 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-07-23 10:16:55 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-07-23 10:16:55 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-07-23 10:16:55 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-23 10:17:00 | INFO | testcases.test_ella.base_ella_test:ella_app:26 | ✅ Ella应用启动成功
2025-07-23 10:17:01 | INFO | testcases.test_ella.base_ella_test:execute_command_and_verify:53 | 初始状态 - 使用命令open flashlight，状态: False
2025-07-23 10:17:03 | INFO | testcases.test_ella.base_ella_test:_execute_command:353 | ✅ 成功执行命令: open flashlight
2025-07-23 10:17:07 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:90 | 状态检查完成，现在获取响应文本
2025-07-23 10:17:07 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:94 | 第1次尝试确保在Ella页面以获取响应
2025-07-23 10:17:07 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:98 | ✅ 已确认在Ella对话页面，可以获取响应
2025-07-23 10:17:14 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:119 | 最终获取的AI响应: '['open flashlight', 'Flashlight is turned on now.', 'Flashlight', '']'
2025-07-23 10:17:14 | INFO | testcases.test_ella.base_ella_test:_verify_status_change:453 | ✅ 状态验证通过: False -> False
2025-07-23 10:17:14 | INFO | core.base_driver:screenshot:306 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaCommandConcise\test_completed.png
2025-07-23 10:17:14 | INFO | testcases.test_ella.base_ella_test:simple_command_test:678 | 🎉 open flashlight 测试完成
2025-07-23 10:17:14 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:490 | verify_expected_in_response 响应类型: <class 'list'>, 内容: ['open flashlight', 'Flashlight is turned on now.', 'Flashlight', '']
2025-07-23 10:17:14 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:534 | ✅ 响应包含期望内容: 'flashlight'
2025-07-23 10:17:14 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:542 | 🎉 所有期望内容都已找到 (1/1)
2025-07-23 10:17:14 | INFO | core.base_driver:screenshot:306 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaCommandConcise\failure_test_open_flashlight_20250723_101714.png
2025-07-23 10:17:14 | INFO | testcases.conftest:take_screenshot_on_failure:102 | 测试失败，已保存截图: D:\aigc\app_test\reports/screenshots\TestEllaCommandConcise\failure_test_open_flashlight_20250723_101714.png
2025-07-23 10:17:15 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-07-23 10:17:15 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-07-23 10:17:15 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
2025-07-23 10:25:39 | INFO | testcases.conftest:setup_test_environment:19 | ==================================================
2025-07-23 10:25:39 | INFO | testcases.conftest:setup_test_environment:20 | 开始测试会话
2025-07-23 10:25:39 | INFO | testcases.conftest:setup_test_environment:21 | ==================================================
2025-07-23 10:25:39 | INFO | testcases.conftest:setup_test_environment:34 | 测试设备信息: {'serial': '13764254B4001229', 'sdk': 35, 'brand': 'TECNO', 'model': 'TECNO CM8', 'arch': 'arm64-v8a', 'version': 15}
2025-07-23 10:25:45 | INFO | testcases.test_ella.base_ella_test:ella_app:26 | ✅ Ella应用启动成功
2025-07-23 10:25:45 | INFO | testcases.test_ella.base_ella_test:execute_command_and_verify:53 | 初始状态 - 使用命令open flashlight，状态: True
2025-07-23 10:25:49 | INFO | testcases.test_ella.base_ella_test:_execute_command:353 | ✅ 成功执行命令: open flashlight
2025-07-23 10:25:53 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:90 | 状态检查完成，现在获取响应文本
2025-07-23 10:25:53 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:94 | 第1次尝试确保在Ella页面以获取响应
2025-07-23 10:25:53 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:98 | ✅ 已确认在Ella对话页面，可以获取响应
2025-07-23 10:26:01 | INFO | testcases.test_ella.base_ella_test:_wait_and_get_response_after_status_check:119 | 最终获取的AI响应: '['open flashlight', 'Flashlight is turned on now.', 'Flashlight', '']'
2025-07-23 10:26:01 | INFO | testcases.test_ella.base_ella_test:_verify_status_change:453 | ✅ 状态验证通过: True -> True
2025-07-23 10:26:01 | INFO | core.base_driver:screenshot:306 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaCommandConcise\test_completed.png
2025-07-23 10:26:01 | INFO | testcases.test_ella.base_ella_test:simple_command_test:678 | 🎉 open flashlight 测试完成
2025-07-23 10:26:01 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:490 | verify_expected_in_response 响应类型: <class 'list'>, 内容: ['open flashlight', 'Flashlight is turned on now.', 'Flashlight', '']
2025-07-23 10:26:01 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:534 | ✅ 响应包含期望内容: 'flashlight'
2025-07-23 10:26:01 | INFO | testcases.test_ella.base_ella_test:verify_expected_in_response:542 | 🎉 所有期望内容都已找到 (1/1)
2025-07-23 10:26:01 | INFO | core.base_driver:screenshot:306 | 截图保存: D:\aigc\app_test\reports/screenshots\TestEllaCommandConcise\test_completed.png
2025-07-23 10:26:02 | INFO | testcases.conftest:setup_test_environment:46 | ==================================================
2025-07-23 10:26:02 | INFO | testcases.conftest:setup_test_environment:47 | 测试会话结束
2025-07-23 10:26:02 | INFO | testcases.conftest:setup_test_environment:48 | ==================================================
