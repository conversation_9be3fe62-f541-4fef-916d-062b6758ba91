"""
Ella状态检查器
负责检查各种系统状态、应用状态和服务状态
"""
import subprocess
import time
from core.logger import log


class EllaStatusChecker:
    """Ella状态检查器"""
    
    def __init__(self, driver=None):
        """
        初始化状态检查器
        
        Args:
            driver: UIAutomator2驱动实例
        """
        self.driver = driver
    
    def check_bluetooth_status(self) -> bool:
        """
        检查蓝牙状态
        
        Returns:
            bool: 蓝牙是否已开启
        """
        try:
            log.info("检查蓝牙状态")
            
            # 通过ADB命令检查蓝牙状态
            result = subprocess.run(
                ["adb", "shell", "settings", "get", "global", "bluetooth_on"],
                capture_output=True,
                text=True,
                timeout=5
            )
            
            if result.returncode == 0:
                bluetooth_status = result.stdout.strip()
                log.info(f"蓝牙状态: (值: {bluetooth_status})")
                is_on = bluetooth_status == "1"
                log.info(f"蓝牙状态: {'开启' if is_on else '关闭'} (值: {bluetooth_status})")
                return is_on
            else:
                log.error(f"获取蓝牙状态失败: {result.stderr}")
                return False
                
        except Exception as e:
            log.error(f"检查蓝牙状态失败: {e}")
            return False
    def check_wifi_status(self) -> bool:
        """
        检查WiFi状态

        Returns:
            bool: WiFi是否已开启
        """
        try:
            log.info("检查WiFi状态")

            # 方法1: 通过ADB命令检查WiFi状态
            result = subprocess.run(
                ["adb", "shell", "settings", "get", "global", "wifi_on"],
                capture_output=True,
                text=True,
                timeout=5
            )

            if result.returncode == 0:
                wifi_status = result.stdout.strip()
                is_on = wifi_status == "1"
                log.info(f"WiFi状态: {'开启' if is_on else '关闭'} (值: {wifi_status})")
                return is_on
            else:
                log.warning(f"方法1获取WiFi状态失败: {result.stderr}")

            # 方法2: 通过dumpsys wifi检查WiFi状态
            result = subprocess.run(
                ["adb", "shell", "dumpsys", "wifi"],
                capture_output=True,
                text=True,
                timeout=10
            )

            if result.returncode == 0:
                wifi_output = result.stdout
                # 检查WiFi是否启用
                if "Wi-Fi is enabled" in wifi_output or "mWifiEnabled: true" in wifi_output:
                    log.info("WiFi状态: 开启 (通过dumpsys检测)")
                    return True
                elif "Wi-Fi is disabled" in wifi_output or "mWifiEnabled: false" in wifi_output:
                    log.info("WiFi状态: 关闭 (通过dumpsys检测)")
                    return False
                else:
                    log.warning("无法从dumpsys输出中确定WiFi状态")

            # 方法3: 通过网络连接状态检查
            result = subprocess.run(
                ["adb", "shell", "dumpsys", "connectivity"],
                capture_output=True,
                text=True,
                timeout=10
            )

            if result.returncode == 0:
                connectivity_output = result.stdout
                if "WIFI" in connectivity_output and "CONNECTED" in connectivity_output:
                    log.info("WiFi状态: 开启且已连接 (通过connectivity检测)")
                    return True

            log.warning("所有方法都无法确定WiFi状态，默认返回False")
            return False

        except Exception as e:
            log.error(f"检查WiFi状态失败: {e}")
            return False
    def check_flashlight_status(self) -> bool:
        """
        检查手电筒状态

        Returns:
            bool: 手电筒是否已开启
        """
        try:
            log.info("检查手电筒状态")

            # 方法1: 通过dumpsys camera检查手电筒状态
            result = subprocess.run(
                ["adb", "shell", "dumpsys", "camera"],
                capture_output=True,
                text=True,
                timeout=10
            )

            if result.returncode == 0:
                camera_output = result.stdout
                # 检查手电筒相关状态
                flashlight_indicators = [
                    "torch",
                    "flashlight",
                    "flash_mode",
                    "TORCH_MODE_ON",
                    "torch_enabled: true"
                ]

                for indicator in flashlight_indicators:
                    if indicator in camera_output.lower():
                        log.info(f"手电筒状态: 开启 (通过camera dumpsys检测到: {indicator})")
                        return True

                # 检查是否明确关闭
                off_indicators = [
                    "torch_enabled: false",
                    "TORCH_MODE_OFF",
                    "flash_mode: off"
                ]

                for indicator in off_indicators:
                    if indicator in camera_output.lower():
                        log.info(f"手电筒状态: 关闭 (通过camera dumpsys检测到: {indicator})")
                        return False
            else:
                log.warning(f"方法1获取手电筒状态失败: {result.stderr}")

            # 方法2: 通过系统属性检查手电筒状态
            result = subprocess.run(
                ["adb", "shell", "getprop", "sys.camera.torch"],
                capture_output=True,
                text=True,
                timeout=5
            )

            if result.returncode == 0:
                torch_prop = result.stdout.strip()
                if torch_prop == "1" or torch_prop.lower() == "true":
                    log.info("手电筒状态: 开启 (通过系统属性检测)")
                    return True
                elif torch_prop == "0" or torch_prop.lower() == "false":
                    log.info("手电筒状态: 关闭 (通过系统属性检测)")
                    return False

            # 方法3: 通过proc文件系统检查LED状态
            result = subprocess.run(
                ["adb", "shell", "cat", "/sys/class/leds/torch/brightness"],
                capture_output=True,
                text=True,
                timeout=5
            )

            if result.returncode == 0:
                brightness = result.stdout.strip()
                try:
                    brightness_value = int(brightness)
                    is_on = brightness_value > 0
                    log.info(f"手电筒状态: {'开启' if is_on else '关闭'} (LED亮度值: {brightness_value})")
                    return is_on
                except ValueError:
                    log.debug(f"无法解析LED亮度值: {brightness}")

            # 方法4: 检查手电筒相关进程
            result = subprocess.run(
                ["adb", "shell", "ps", "|", "grep", "-i", "torch"],
                capture_output=True,
                text=True,
                timeout=5
            )

            if result.returncode == 0 and result.stdout.strip():
                log.info("手电筒状态: 可能开启 (检测到torch相关进程)")
                return True

            log.info("手电筒状态: 关闭 (所有检测方法均未发现开启状态)")
            return False

        except Exception as e:
            log.error(f"检查手电筒状态失败: {e}")
            return False

    def get_flashlight_detailed_status(self) -> dict:
        """
        获取手电筒详细状态信息

        Returns:
            dict: 手电筒详细状态信息
        """
        try:
            log.info("获取手电筒详细状态")

            status_info = {
                'flashlight_enabled': False,
                'brightness_level': 0,
                'torch_mode': 'OFF',
                'camera_in_use': False,
                'detection_method': None
            }

            # 检查基本状态
            status_info['flashlight_enabled'] = self.check_flashlight_status()

            # 获取详细的LED亮度信息
            result = subprocess.run(
                ["adb", "shell", "cat", "/sys/class/leds/torch/brightness"],
                capture_output=True,
                text=True,
                timeout=5
            )

            if result.returncode == 0:
                brightness = result.stdout.strip()
                try:
                    status_info['brightness_level'] = int(brightness)
                    status_info['detection_method'] = 'LED_BRIGHTNESS'
                    log.info(f"LED亮度级别: {status_info['brightness_level']}")
                except ValueError:
                    log.debug(f"无法解析LED亮度值: {brightness}")

            # 检查最大亮度
            max_brightness_result = subprocess.run(
                ["adb", "shell", "cat", "/sys/class/leds/torch/max_brightness"],
                capture_output=True,
                text=True,
                timeout=5
            )

            if max_brightness_result.returncode == 0:
                try:
                    max_brightness = int(max_brightness_result.stdout.strip())
                    status_info['max_brightness'] = max_brightness
                    if status_info['brightness_level'] > 0:
                        status_info['brightness_percentage'] = (status_info['brightness_level'] / max_brightness) * 100
                        log.info(f"亮度百分比: {status_info['brightness_percentage']:.1f}%")
                except ValueError:
                    pass

            # 检查相机服务状态
            camera_result = subprocess.run(
                ["adb", "shell", "dumpsys", "camera"],
                capture_output=True,
                text=True,
                timeout=10
            )

            if camera_result.returncode == 0:
                camera_output = camera_result.stdout
                if "Camera in use" in camera_output or "camera_in_use: true" in camera_output:
                    status_info['camera_in_use'] = True
                    log.info("相机正在使用中")

                # 解析torch模式
                if "TORCH_MODE_ON" in camera_output:
                    status_info['torch_mode'] = 'ON'
                elif "TORCH_MODE_OFF" in camera_output:
                    status_info['torch_mode'] = 'OFF'

            return status_info

        except Exception as e:
            log.error(f"获取手电筒详细状态失败: {e}")
            return {
                'flashlight_enabled': False,
                'brightness_level': 0,
                'torch_mode': 'UNKNOWN',
                'camera_in_use': False,
                'detection_method': 'ERROR'
            }

    def check_wifi_connection_status(self) -> dict:
        """
        检查WiFi连接详细状态

        Returns:
            dict: WiFi连接状态信息
        """
        try:
            log.info("检查WiFi连接详细状态")

            status_info = {
                'wifi_enabled': False,
                'connected': False,
                'ssid': None,
                'signal_strength': None,
                'ip_address': None
            }

            # 检查WiFi是否启用
            status_info['wifi_enabled'] = self.check_wifi_status()

            if not status_info['wifi_enabled']:
                log.info("WiFi未启用")
                return status_info

            # 获取WiFi连接信息
            result = subprocess.run(
                ["adb", "shell", "dumpsys", "wifi"],
                capture_output=True,
                text=True,
                timeout=10
            )

            if result.returncode == 0:
                wifi_output = result.stdout

                # 解析SSID
                import re
                ssid_match = re.search(r'SSID: "([^"]+)"', wifi_output)
                if ssid_match:
                    status_info['ssid'] = ssid_match.group(1)
                    status_info['connected'] = True
                    log.info(f"已连接到WiFi: {status_info['ssid']}")

                # 解析信号强度
                signal_match = re.search(r'RSSI: (-?\d+)', wifi_output)
                if signal_match:
                    status_info['signal_strength'] = int(signal_match.group(1))
                    log.info(f"信号强度: {status_info['signal_strength']} dBm")

            # 获取IP地址
            if status_info['connected']:
                ip_result = subprocess.run(
                    ["adb", "shell", "ip", "route", "get", "*******"],
                    capture_output=True,
                    text=True,
                    timeout=5
                )

                if ip_result.returncode == 0:
                    ip_output = ip_result.stdout
                    ip_match = re.search(r'src (\d+\.\d+\.\d+\.\d+)', ip_output)
                    if ip_match:
                        status_info['ip_address'] = ip_match.group(1)
                        log.info(f"IP地址: {status_info['ip_address']}")

            return status_info

        except Exception as e:
            log.error(f"检查WiFi连接状态失败: {e}")
            return {
                'wifi_enabled': False,
                'connected': False,
                'ssid': None,
                'signal_strength': None,
                'ip_address': None
            }

    def check_alarm_status(self) -> bool:
        """
        检查闹钟状态 - 通过ADB命令获取闹钟列表
        
        Returns:
            bool: 是否有设置的闹钟
        """
        try:
            log.info("检查闹钟状态")
            
            # 通过ADB命令检查闹钟设置
            result = subprocess.run(
                ["adb", "shell", "dumpsys", "alarm"],
                capture_output=True,
                text=True,
                timeout=10
            )
            
            if result.returncode == 0:
                alarm_output = result.stdout
                # 检查是否包含闹钟相关信息
                alarm_indicators = [
                    "com.transsion.deskclock",
                    "AlarmManager",
                    "alarm_clock",
                    "RTC_WAKEUP"
                ]
                
                for indicator in alarm_indicators:
                    if indicator in alarm_output:
                        log.info(f"检测到闹钟相关信息: {indicator}")
                        return True
                
                log.info("未检测到活跃的闹钟")
                return False
            else:
                log.error(f"获取闹钟状态失败: {result.stderr}")
                return False
                
        except Exception as e:
            log.error(f"检查闹钟状态失败: {e}")
            return False
    
    def ensure_ella_process(self) -> bool:
        """
        确保当前进程是Ella应用
        
        Returns:
            bool: 当前是否在Ella进程
        """
        try:
            if not self.driver:
                log.error("驱动实例未初始化")
                return False
                
            log.info("检查当前进程是否是Ella...")
            
            # 获取当前前台应用信息
            current_app = self.driver.app_current()
            current_package = current_app.get('package', '')
            current_activity = current_app.get('activity', '')
            
            log.info(f"当前应用: {current_package}")
            log.info(f"当前Activity: {current_activity}")
            
            # 检查是否是Ella应用
            ella_packages = [
                "com.transsion.aivoiceassistant",
                "com.transsion.ella"
            ]
            
            if current_package in ella_packages:
                log.info("✅ 当前在Ella应用进程")
                return True
            else:
                log.warning(f"❌ 当前不在Ella应用进程，当前包名: {current_package}")
                return False
                
        except Exception as e:
            log.error(f"检查Ella进程失败: {e}")
            return False
    
    def check_service_health(self) -> bool:
        """
        检查UIAutomator2服务健康状态
        
        Returns:
            bool: 服务是否健康
        """
        try:
            if not self.driver:
                log.error("驱动实例未初始化")
                return False
                
            log.info("检查UIAutomator2服务健康状态")
            
            # 检查设备信息是否可以正常获取
            device_info = self.driver.device_info
            
            # 检查关键信息是否存在
            if not device_info or not device_info.get('serial'):
                log.warning("设备信息不完整")
                return False
            
            # 尝试获取屏幕信息
            window_size = self.driver.window_size()
            if not window_size or len(window_size) != 2:
                log.warning("无法获取屏幕信息")
                return False
            
            log.info("✅ UIAutomator2服务健康状态良好")
            return True
            
        except Exception as e:
            log.warning(f"UIAutomator2服务健康检查失败: {e}")
            return False
    
    def check_app_started(self, package_name: str) -> bool:
        """
        检查应用是否启动成功
        
        Args:
            package_name: 应用包名
            
        Returns:
            bool: 应用是否启动成功
        """
        try:
            if not self.driver:
                log.error("驱动实例未初始化")
                return False
                
            # 方法1: 检查当前前台应用
            current_app = self.driver.app_current()
            current_package = current_app.get('package', '')
            
            if current_package == package_name:
                log.info(f"✅ 应用已在前台: {current_package}")
                return True
            
            # 方法2: 检查应用是否在运行
            running_apps = self.driver.app_list_running()
            if package_name in running_apps:
                log.info(f"✅ 应用正在运行: {package_name}")
                # 尝试切换到前台
                self.driver.app_start(package_name)
                time.sleep(1)
                return True
            
            return False
            
        except Exception as e:
            log.error(f"检查应用启动状态失败: {e}")
            return False
    
    def get_alarm_list(self) -> list:
        """
        获取闹钟列表
        
        Returns:
            list: 闹钟列表
        """
        try:
            log.info("获取闹钟列表")
            alarms = []
            
            # 方法1: 通过dumpsys alarm获取
            result = subprocess.run(
                ["adb", "shell", "dumpsys", "alarm"],
                capture_output=True,
                text=True,
                timeout=15
            )
            
            if result.returncode == 0:
                alarm_output = result.stdout
                # 解析闹钟信息
                alarms.extend(self._parse_alarm_database_output(alarm_output))
            
            log.info(f"找到 {len(alarms)} 个闹钟")
            return alarms
            
        except Exception as e:
            log.error(f"获取闹钟列表失败: {e}")
            return []
    
    def _parse_alarm_database_output(self, output: str) -> list:
        """
        解析闹钟数据库输出
        
        Args:
            output: 数据库输出内容
            
        Returns:
            list: 解析出的闹钟列表
        """
        alarms = []
        try:
            lines = output.split('\n')
            for line in lines:
                line = line.strip()
                # 查找包含时间信息的行
                if any(keyword in line.lower() for keyword in ['alarm', 'clock', 'time']):
                    # 简单的时间模式匹配
                    import re
                    time_pattern = r'\b([0-1]?[0-9]|2[0-3]):[0-5][0-9]\b'
                    matches = re.findall(time_pattern, line)
                    for match in matches:
                        if match not in [alarm.get('time') for alarm in alarms]:
                            alarms.append({
                                'time': match,
                                'enabled': 'enabled' in line.lower() or 'on' in line.lower(),
                                'source': 'dumpsys'
                            })
        except Exception as e:
            log.debug(f"解析闹钟输出失败: {e}")
        
        return alarms
    
    def clear_all_alarms(self) -> bool:
        """
        清除所有闹钟
        
        Returns:
            bool: 是否成功清除
        """
        try:
            log.info("清除所有闹钟")
            
            # 通过ADB命令清除闹钟
            commands = [
                ["adb", "shell", "am", "broadcast", "-a", "android.intent.action.ALARM_CHANGED"],
                ["adb", "shell", "settings", "put", "system", "alarm_alert", ""],
            ]
            
            success_count = 0
            for cmd in commands:
                try:
                    result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
                    if result.returncode == 0:
                        success_count += 1
                except Exception as e:
                    log.debug(f"执行清除命令失败: {e}")
            
            success = success_count > 0
            log.info(f"闹钟清除{'成功' if success else '失败'}")
            return success
            
        except Exception as e:
            log.error(f"清除闹钟失败: {e}")
            return False


if __name__ == '__main__':
    from core.base_driver import driver_manager
    checker = EllaStatusChecker(driver_manager.driver)

    # # 测试蓝牙状态
    # print("蓝牙状态:", checker.check_bluetooth_status())
    #
    # # 测试WiFi状态
    # print("WiFi状态:", checker.check_wifi_status())
    #
    # # 测试WiFi连接详细状态
    # wifi_info = checker.check_wifi_connection_status()
    # print("WiFi详细信息:", wifi_info)

    # 测试手电筒状态
    print("手电筒状态:", checker.check_flashlight_status())

    # 测试手电筒详细状态
    # flashlight_info = checker.get_flashlight_detailed_status()
    # print("手电筒详细信息:", flashlight_info)

    # 测试闹钟状态
    # print("闹钟列表:", checker.get_alarm_list())

