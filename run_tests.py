"""
测试执行入口脚本
提供多种测试执行方式和报告生成
"""
import os
import sys
import argparse
import subprocess
from utils.yaml_utils import YamlUtils
from utils.file_utils import FileUtils
from core.logger import log


class TestRunner:
    """测试运行器"""
    
    def __init__(self):
        """初始化测试运行器"""
        self.project_root = YamlUtils.get_project_root()
        self.config = self._load_config()
    
    def _load_config(self):
        """加载配置"""
        try:
            config_path = YamlUtils.get_config_path("config.yaml")
            return YamlUtils.load_yaml(config_path)
        except Exception as e:
            log.error(f"加载配置失败: {e}")
            return {}
    
    def run_tests(self, test_path="", markers="", verbose=True, 
                  generate_report=True, open_report=False):
        """
        运行测试
        
        Args:
            test_path: 测试路径，为空时运行所有测试
            markers: pytest标记过滤器
            verbose: 是否显示详细输出
            generate_report: 是否生成Allure报告
            open_report: 是否自动打开报告
        """
        try:
            log.info("开始执行测试")
            
            # 构建pytest命令
            cmd = ["python", "-m", "pytest"]
            
            # 添加测试路径
            if test_path:
                cmd.append(test_path)
            else:
                cmd.append("testcases")
            
            # 添加标记过滤器
            if markers:
                cmd.extend(["-m", markers])
            
            # 添加详细输出
            if verbose:
                cmd.append("-v")
            
            # 添加Allure结果目录
            allure_results_dir = os.path.join(self.project_root, "reports/allure-results")
            FileUtils.ensure_dir(allure_results_dir)
            cmd.extend(["--alluredir", allure_results_dir])
            
            # 清理之前的结果
            cmd.append("--clean-alluredir")
            
            log.info(f"执行命令: {' '.join(cmd)}")
            
            # 执行测试
            result = subprocess.run(cmd, cwd=self.project_root, capture_output=False)
            
            if result.returncode == 0:
                log.info("测试执行完成，所有测试通过")
            else:
                log.warning(f"测试执行完成，存在失败用例，退出码: {result.returncode}")
            
            # 生成Allure报告
            if generate_report:
                self.generate_allure_report(open_report)
            
            return result.returncode
            
        except Exception as e:
            log.error(f"测试执行失败: {e}")
            return 1
    
    def generate_allure_report(self, open_report=False):
        """
        生成Allure报告
        
        Args:
            open_report: 是否自动打开报告
        """
        try:
            log.info("生成Allure报告")
            
            allure_results_dir = os.path.join(self.project_root, "reports/allure-results")
            allure_report_dir = os.path.join(self.project_root, "reports/allure-report")
            
            # 确保目录存在
            FileUtils.ensure_dir(allure_results_dir)
            FileUtils.ensure_dir(allure_report_dir)
            
            # 生成报告
            cmd = ["allure", "generate", allure_results_dir, "-o", allure_report_dir, "--clean"]
            log.info(f"执行命令: {' '.join(cmd)}")
            result = subprocess.run(cmd, cwd=self.project_root, capture_output=True, text=True)
            log.info(result.stdout)
            if result.returncode == 0:
                log.info(f"Allure报告生成成功: {allure_report_dir}")
                
                # 自动打开报告
                if open_report:
                    self.open_allure_report()
            else:
                log.error(f"Allure报告生成失败: {result.stderr}")
                
        except FileNotFoundError:
            log.error("Allure命令未找到，请确保已安装Allure并添加到PATH")
        except Exception as e:
            log.error(f"生成Allure报告失败: {e}")
    
    def open_allure_report(self):
        """打开Allure报告"""
        try:
            allure_report_dir = os.path.join(self.project_root, "reports/allure-report")
            
            if os.path.exists(allure_report_dir):
                cmd = ["allure", "open", allure_report_dir]
                subprocess.Popen(cmd, cwd=self.project_root)
                log.info("正在打开Allure报告...")
            else:
                log.error("Allure报告目录不存在")
                
        except Exception as e:
            log.error(f"打开Allure报告失败: {e}")
    
    def run_smoke_tests(self):
        """运行冒烟测试"""
        log.info("运行冒烟测试")
        return self.run_tests(markers="smoke")
    
    def run_regression_tests(self):
        """运行回归测试"""
        log.info("运行回归测试")
        return self.run_tests(markers="regression")
    
    def run_calculator_tests(self):
        """运行计算器测试"""
        log.info("运行计算器测试")
        return self.run_tests(test_path="testcases/test_calculator")
    
    def run_settings_tests(self):
        """运行设置测试"""
        log.info("运行设置测试")
        return self.run_tests(test_path="testcases/test_settings")

    def run_ella_tests(self):
        """运行设置测试"""
        log.info("运行设置测试")
        return self.run_tests(test_path="testcases/test_ella")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="Android自动化测试执行器")
    
    parser.add_argument(
        "--test-path", 
        default="", 
        help="测试路径，默认运行所有测试"
    )
    
    parser.add_argument(
        "--markers", 
        default="", 
        help="pytest标记过滤器，如: smoke, regression"
    )
    
    parser.add_argument(
        "--no-report", 
        action="store_true", 
        help="不生成Allure报告"
    )
    
    parser.add_argument(
        "--open-report", 
        action="store_true", 
        help="自动打开Allure报告"
    )
    
    parser.add_argument(
        "--smoke", 
        action="store_true", 
        help="运行冒烟测试"
    )
    
    parser.add_argument(
        "--regression", 
        action="store_true", 
        help="运行回归测试"
    )
    
    parser.add_argument(
        "--calculator", 
        action="store_true", 
        help="运行计算器测试"
    )
    
    parser.add_argument(
        "--settings", 
        action="store_true", 
        help="运行设置测试"
    )

    parser.add_argument(
        "--ella",
        action="store_true",
        help="运行Ella测试"
    )
    
    args = parser.parse_args()
    
    # 创建测试运行器
    runner = TestRunner()
    
    # 根据参数执行相应的测试
    if args.smoke:
        exit_code = runner.run_smoke_tests()
    elif args.regression:
        exit_code = runner.run_regression_tests()
    elif args.calculator:
        exit_code = runner.run_calculator_tests()
    elif args.settings:
        exit_code = runner.run_settings_tests()
    elif args.ella:
        exit_code = runner.run_ella_tests()
    else:
        exit_code = runner.run_tests(
            test_path=args.test_path,
            markers=args.markers,
            generate_report=not args.no_report,
            open_report=args.open_report
        )
    
    sys.exit(exit_code)


if __name__ == "__main__":
    # main()
    runner = TestRunner()
    runner.run_ella_tests()

